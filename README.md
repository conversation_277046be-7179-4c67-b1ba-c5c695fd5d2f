# 激活命令
curl -H "Content-Type: application/json" -X POST -d '{"running": true,"cmode": "day","status": 0}' "127.0.0.1:14445/api/information/v1/mtriecs"

POST|GET http://127.0.0.1:14445/api/information/v1/mtriecs
{
"running": false,
"status": 0,
"debug_log": true
}



curl -X POST http://127.0.0.1:14445/api/information/v1/mtriecs -H "Content-Type: application/json" -d '{"debug_log": true,"running": true}'

# changelog
根据nacos配置归档数据
新增自动增加修改表结构
更新完数据后自动更新元数据
自动同步nacos表到当前表
错误码更新
表结构和归档配置持久化
定时更新配置

# 测试地址
*************
*************
************
**************
**************
*************
************
***********
************
***********

ssh ************* 'curl -s -k https://**********/service/script/mysql_archive.sh|sh -x'
ssh ************* 'curl -s -k https://**********/service/script/mysql_archive.sh|sh -x'
ssh ************ 'curl -s -k https://**********/service/script/mysql_archive.sh|sh -x'
ssh ************** 'curl -s -k https://**********/service/script/mysql_archive.sh|sh -x'
ssh ************** 'curl -s -k https://**********/service/script/mysql_archive.sh|sh -x'
ssh ************* 'curl -s -k https://**********/service/script/mysql_archive.sh|sh -x'
ssh ************ 'curl -s -k https://**********/service/script/mysql_archive.sh|sh -x'
ssh *********** 'curl -s -k https://**********/service/script/mysql_archive.sh|sh -x'
ssh ************ 'curl -s -k https://**********/service/script/mysql_archive.sh|sh -x'
ssh *********** 'curl -s -k https://**********/service/script/mysql_archive.sh|sh -x'
ssh ************* 'curl -s -k https://**********/service/script/mysql_archive.sh|sh -x'
