#!/usr/bin/env bash
# curl -s -k https://**********/service/script/mysql_archive.sh|sh -x
Lockfile='/tmp/mysql_archive.pid'
function wpid(){
  # 判断是否有写入权限
  if [ ! -w "$Lockfile" ]; then
      chmod +w "$Lockfile"
      echo "$$" > "$Lockfile"
  else
     echo "$$" > "$Lockfile"
  fi
}

# 判断进程是否已经运行
function exist_check() {
	# 判断文件是否存在
	if [ -f "$Lockfile" ]; then
		# 判断pid是否为空
		pid=$(cat $Lockfile)
		if [ ! -z $pid ]; then
			# 判断进程是否存在
			ProcNumber=$(ps -ef | grep -w $pid | grep -v grep | wc -l)
			if [ $ProcNumber -le 0 ]; then
				echo '程序没有运行'
				wpid
			else
				echo '程序已运行'
				exit 0
			fi
		fi
	else
		touch "$Lockfile"
		wpid
	fi
}

function init(){
  exist_check
  if [ $(cat /etc/redhat-release|grep '7.6'|wc -l ) == 0 ]; then
    echo '非centos7.6.1810'
    exit 1111
    fi
}

# 下载文件
function download(){
    if [ `uname -a |grep 'x86'|wc -l` = 1 ];
    then
       curl -s -k https://**********/service/package/mysql_archive/x86/mysql_archive.tgz -o /tmp/mysql_archive.tgz
       md5=`curl -s -k 'https://**********/service/package/mysql_archive/x86/mysql_archive.tgz.md5'|awk '{print $1}'`
    else
       curl -s -k https://**********/service/package/mysql_archive/arm/mysql_archive.tgz -o /tmp/mysql_archive.tgz
       md5=`curl -s -k 'https://**********/service/package/mysql_archive/arm/mysql_archive.tgz.md5'|awk '{print $1}'`
    fi

  if [ `/usr/bin/md5sum /tmp/mysql_archive.tgz|awk '{print $1}'` == "$md5" ];
  then
     echo 'md5验证成功'
  else
    exit 1
  fi
}

function clean(){
  # 清理服务和文件
  if [ -f '/usr/lib/systemd/system/mysql_archive.service' ]; then
    systemctl stop mysql_archive.service
    rm -rf /usr/lib/systemd/system/mysql_archive.service
  fi
  #
  if [ -f '/usr/local/mysql_archive' ]; then
    rm -rf /usr/local/mysql_archive
  fi
}

function install(){
    # 解压
    cd /tmp/
    if [ -f 'mysql_archive.tgz' ]; then
      tar -zxvf mysql_archive.tgz
      if [ $(echo $?) == 0 ]; then
        # 清理旧版本
        clean
        # 安装配置
        cp -r /tmp/mysql_archive/mysql_archive /usr/local/mysql_archive
        cp -r /tmp/mysql_archive/mysql_archive.service /usr/lib/systemd/system/mysql_archive.service
        systemctl enable mysql_archive.service
        systemctl start mysql_archive.service
      fi
    fi
}

function validation() {
  rs=`systemctl status mysql_archive.service|grep Active|wc -l`
  if [ $rs = 1 ]; then
    echo "安装成功"
  else
    echo "安装失败"
    exit 1
  fi
}

# 判断是否最新版
init
# 下载文件
download
# 安装
install
# 验证
validation
