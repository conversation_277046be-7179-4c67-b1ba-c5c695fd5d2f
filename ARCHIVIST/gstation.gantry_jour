CREATE TABLE IF NOT EXISTS `gantry_jour` (
  `vc_trade_id` varchar(38) NOT NULL COMMENT '计费交易编号（唯一索引）',
  `vc_gantry_id` varchar(19) NOT NULL COMMENT '门架编号',
  `n_sect_gantry_id` int(5) DEFAULT NULL COMMENT '省内门架编号',
  `n_computer_order` int(1) DEFAULT NULL COMMENT '控制器序号',
  `vc_hour_batch_no` varchar(10) NOT NULL COMMENT '小时批次号',
  `n_gantry_order_num` int(3) NOT NULL COMMENT '门架顺序号',
  `vc_gantry_hex` varchar(6) NOT NULL COMMENT '门架Hex值',
  `vc_gantry_hex_opposite` varchar(400) NOT NULL,
  `ts_trans_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '计费交易时间',
  `d_pay_fee` decimal(12,2) NOT NULL COMMENT '应收金额',
  `d_discount_fee` decimal(12,2) NOT NULL COMMENT '优惠金额',
  `d_trans_fee` decimal(12,2) NOT NULL COMMENT '卡面扣费金额',
  `n_media_type` tinyint(3) unsigned DEFAULT NULL COMMENT '通行介质类型',
  `n_obu_sign` tinyint(3) unsigned DEFAULT NULL COMMENT 'OBU单/双片标识',
  `vc_toll_interval_id` varchar(850) DEFAULT NULL COMMENT '收费单元编号',
  `vc_pay_fee_group` varchar(400) DEFAULT NULL COMMENT '应收金额组合',
  `vc_discount_fee_group` varchar(400) DEFAULT NULL COMMENT '优惠金额组合',
  `vc_vehicle_plate` varchar(20) DEFAULT NULL COMMENT '计费车辆车牌号码+颜色',
  `n_vehicle_type` tinyint(3) unsigned NOT NULL COMMENT '计费车型',
  `n_identify_vehicle_type` tinyint(3) unsigned DEFAULT NULL COMMENT '识别车型',
  `n_vehicle_class` tinyint(3) unsigned NOT NULL COMMENT '车种',
  `vc_tac` varchar(8) DEFAULT NULL COMMENT 'TAC码',
  `n_trans_type` int(6) DEFAULT NULL COMMENT '交易类型标识',
  `vc_terminal_no` varchar(12) DEFAULT NULL COMMENT '终端机编号',
  `vc_terminal_trans_no` varchar(8) DEFAULT NULL COMMENT '终端机交易序号',
  `vc_trans_no` varchar(8) DEFAULT NULL,
  `n_service_type` tinyint(4) DEFAULT NULL COMMENT '交易的服务类型',
  `c_algorithm_identifier` tinyint(4) DEFAULT NULL COMMENT '算法标识',
  `c_key_version` varchar(2) DEFAULT NULL COMMENT '密钥版本号',
  `n_antenna_id` int(8) DEFAULT NULL COMMENT '天线ID编号',
  `vc_rate_version` varchar(40) DEFAULT NULL COMMENT '计费模块和计费参数版本号',
  `n_duration` int(8) DEFAULT NULL COMMENT '交易耗时',
  `n_pass_state` tinyint(3) unsigned DEFAULT NULL COMMENT '通行状态',
  `vc_en_toll_lane_id` varchar(21) DEFAULT NULL COMMENT '入口车道编号',
  `vc_en_toll_station_hex` varchar(8) DEFAULT NULL COMMENT '入口站hex字符串',
  `ts_en_time` datetime NOT NULL DEFAULT '2019-01-01 08:00:00' COMMENT '入口交易发生的时间',
  `n_en_lane_type` tinyint(3) unsigned DEFAULT NULL COMMENT '入口车道类型',
  `vc_pass_id` varchar(40) DEFAULT NULL COMMENT '通行标识ID',
  `vc_last_gantry_hex` varchar(6) DEFAULT NULL COMMENT '上一个门架的hex编号',
  `ts_last_gantry_time` datetime NOT NULL DEFAULT '2019-01-01 01:00:00' COMMENT '通过上一门架的时间',
  `vc_pass_macid` varchar(8) DEFAULT NULL COMMENT 'OBU/CPC MAC地址',
  `vc_pass_issuerid` varchar(16) DEFAULT NULL COMMENT 'OBU/CPC发行方标识',
  `vc_obu_sn` varchar(20) NOT NULL COMMENT 'OBU/CPC序列编号',
  `vc_obu_version` tinyint(3) unsigned DEFAULT NULL COMMENT 'OBU/CPC版本号',
  `n_obu_start_date` int(8) DEFAULT NULL COMMENT 'OBU/CPC起始日期',
  `n_obu_end_date` int(8) DEFAULT NULL COMMENT 'OBU/CPC截止日期',
  `n_obu_electrical` tinyint(4) DEFAULT NULL,
  `vc_obu_state` varchar(4) DEFAULT NULL COMMENT 'OBU/CPU状态',
  `vc_obu_vehicle_plate` varchar(20) DEFAULT NULL COMMENT 'OBU内车牌号码',
  `n_obu_vehicle_type` tinyint(3) unsigned DEFAULT NULL COMMENT 'OBU内车型',
  `n_vehicle_user_type` tinyint(3) unsigned DEFAULT NULL COMMENT '车辆用户类型',
  `n_vehicle_seat` int(8) DEFAULT NULL COMMENT '车辆座位数/载重',
  `n_axle_count` tinyint(3) unsigned DEFAULT NULL COMMENT '车轴数',
  `n_total_weight` int(8) DEFAULT NULL COMMENT '车货总重',
  `n_vehicle_length` int(8) DEFAULT NULL COMMENT '车辆长',
  `n_vehicle_width` int(8) DEFAULT NULL COMMENT '车辆宽',
  `n_vehicle_height` int(8) DEFAULT NULL COMMENT '车辆高',
  `vc_cpu_net_id` varchar(4) DEFAULT NULL COMMENT 'CPU卡片网络编号',
  `vc_cpu_issue_id` varchar(16) DEFAULT NULL COMMENT 'CPU卡片发行方标识',
  `vc_cpu_vehicle_plate` varchar(20) DEFAULT NULL COMMENT 'CPU内车牌号码',
  `n_cpu_vehicle_type` tinyint(3) unsigned DEFAULT NULL COMMENT 'CPU内车型',
  `n_cpu_start_date` int(8) DEFAULT NULL COMMENT 'CPU起始日期',
  `n_cpu_end_date` int(8) DEFAULT NULL COMMENT 'CPU截止日期',
  `n_cpu_version` tinyint(3) unsigned DEFAULT NULL COMMENT 'CPU用户卡版本',
  `n_cpu_card_type` tinyint(3) unsigned DEFAULT NULL COMMENT 'CPU卡类型',
  `vc_cpu_card_id` varchar(20) DEFAULT NULL COMMENT 'CPU卡编号',
  `d_balance_before` decimal(12,2) DEFAULT NULL COMMENT '交易前金额',
  `d_balance_after` decimal(12,2) DEFAULT NULL COMMENT '交易后金额',
  `n_gantry_pass_count` tinyint(3) unsigned DEFAULT NULL COMMENT 'CPC卡过站信息数量',
  `vc_gantry_pass_info` varchar(1500) DEFAULT NULL COMMENT 'CPC卡过站信息',
  `vc_fee_prov_info` varchar(20) DEFAULT NULL COMMENT 'CPC卡省内计费信息',
  `d_fee_sum_local_before` decimal(12,2) DEFAULT NULL COMMENT 'CPC卡本省费用累计前金额',
  `n_fee_calc_result` int(8) DEFAULT NULL COMMENT '计费接口特情值',
  `vc_fee_info1` varchar(400) DEFAULT NULL COMMENT '计费信息1',
  `vc_fee_info2` varchar(400) DEFAULT NULL COMMENT '计费信息2',
  `vc_fee_info3` varchar(400) DEFAULT NULL COMMENT '计费信息3',
  `n_holiday_state` tinyint(3) unsigned DEFAULT NULL COMMENT '节假日状态',
  `n_trade_result` tinyint(3) unsigned NOT NULL COMMENT '交易结果',
  `vc_special_type` varchar(100) DEFAULT NULL COMMENT '特情类型',
  `vc_verify_code` varchar(32) NOT NULL COMMENT '校验码',
  `vc_interrupt_signal` tinyint(4) DEFAULT NULL COMMENT '干扰信号',
  `vc_vehicle_pic_id` varchar(38) DEFAULT NULL COMMENT '车牌识别流水号',
  `vc_vehicle_tail_pic_id` varchar(38) DEFAULT NULL COMMENT '车牌识别流水号(车尾)',
  `n_match_status` tinyint(3) unsigned NOT NULL COMMENT '匹配状态',
  `n_valid_status` tinyint(3) unsigned NOT NULL COMMENT '去重状态',
  `n_deal_status` tinyint(3) unsigned NOT NULL COMMENT '处理状态',
  `vc_related_trade_id` varchar(3800) DEFAULT NULL,
  `vc_all_related_trade_id` varchar(3800) DEFAULT NULL,
  `ts_station_db_time` datetime NOT NULL DEFAULT '2019-01-01 08:00:00' COMMENT '门架后台入库时间',
  `ts_station_deal_time` datetime NOT NULL DEFAULT '2019-01-01 08:00:00' COMMENT '门架后台处理时间',
  `ts_station_valid_time` datetime NOT NULL DEFAULT '2019-01-01 08:00:00' COMMENT '门架后台去重时间',
  `ts_station_match_time` datetime NOT NULL DEFAULT '2019-01-01 08:00:00' COMMENT '门架后台匹配时间',
  `c_mot_send_flag` tinyint(4) DEFAULT '0' COMMENT '部中心发送标志',
  `c_center_send_flag` tinyint(4) DEFAULT '0' COMMENT '省中心发送标志',
  `c_midcenter_send_flag` tinyint(4) DEFAULT '0' COMMENT '路段中心发送标志',
  `c_subcenter_send_flag` tinyint(4) DEFAULT '0' COMMENT '分中心发送标志',
  `c_station_send_flag` tinyint(4) DEFAULT '0' COMMENT '收费站发送标志',
  `n_station_id` int(4) NOT NULL COMMENT '收费站编码',
  `vc_last_gantry_hex_pass` varchar(6) DEFAULT NULL COMMENT '过站信息中上一个门架的hex编号',
  `vc_vehicle_sign` varchar(100) NOT NULL DEFAULT '0xff',
  `n_fee_calc_special` int(4) NOT NULL DEFAULT '-1',
  `vc_last_gantry_hex_fee` varchar(6) DEFAULT '255',
  `vc_gantry_type` varchar(1) DEFAULT '0',
  `d_obu_prov_fee_sum_before` decimal(12,2) DEFAULT '-1.00' COMMENT '本次交易前标签累计金额（省内）',
  `d_obu_prov_fee_sum_after` decimal(12,2) DEFAULT '-1.00' COMMENT '本次交易后标签累计金额（省内）',
  `d_card_fee_sum_before` decimal(12,2) DEFAULT '-1.00' COMMENT '本次交易前卡片累计金额',
  `d_card_fee_sum_after` decimal(12,2) DEFAULT '-1.00' COMMENT '本次交易后卡片累计金额',
  `n_nocard_times_before` int(11) DEFAULT '-1' COMMENT '本次交易前累计无卡次数',
  `n_nocard_times_after` int(11) DEFAULT '-1' COMMENT '本次交易后累计无卡次数',
  `n_province_num_before` int(11) DEFAULT '-1' COMMENT '本次交易前累计省份数量',
  `n_province_num_after` int(11) DEFAULT '-1' COMMENT '本次交易前后计省份数量',
  `n_obu_total_trade_succ_num_before` int(11) DEFAULT '-1' COMMENT '本次交易前标签累计写入成功总量',
  `n_obu_total_trade_succ_num_after` int(11) DEFAULT '-1' COMMENT '本次交易后标签累计写入成功总量',
  `n_obu_prov_trade_succ_num_before` int(11) DEFAULT '-1' COMMENT '省内本次交易前标签累计写入成功数量',
  `n_obu_prov_trade_succ_num_after` int(11) DEFAULT '-1' COMMENT '省内本次交易后标签累计写入成功数量',
  `n_obu_trade_result` tinyint(4) DEFAULT '-1' COMMENT '标签交易结果 0-成功，1-失败',
  `n_trade_type` tinyint(4) DEFAULT '-1' COMMENT '交易类型 0-复合消费+标签写卡，1-仅标签交易，2-仅复合消费',
  `n_obu_info_type_read` tinyint(4) DEFAULT '-1' COMMENT '读取标签入口信息类型',
  `n_obu_info_type_write` tinyint(4) DEFAULT '-1' COMMENT '本次写入标签入口信息类型',
  `n_obu_pass_state` tinyint(4) DEFAULT '-1' COMMENT '标签入口状态',
  `n_fee_vehicle_type` tinyint(4) DEFAULT '-1' COMMENT '计费车型',
  `vc_obu_last_gantry_hex` varchar(6) DEFAULT NULL COMMENT 'OBU中上一个门架HEX',
  `ts_obu_last_gantry_time` datetime DEFAULT NULL COMMENT 'OBU中通过上一个门架时间',
  `d_obu_pay_fee_sum_before` decimal(12,2) DEFAULT '-1.00' COMMENT '本次交易前标签累计应收金额',
  `d_obu_pay_fee_sum_after` decimal(12,2) DEFAULT '-1.00' COMMENT '本次交易后标签累计应收金额',
  `d_obu_discount_fee_sum_before` decimal(12,2) DEFAULT '-1.00' COMMENT '本次交易前标签累计优惠金额',
  `d_obu_discount_fee_sum_after` decimal(12,2) DEFAULT '-1.00' COMMENT '本次交易后标签累计优惠金额',
  `n_obu_mileage_before` bigint(20) DEFAULT '-1' COMMENT '本次交易前标签累计里程，单位米',
  `n_obu_mileage_after` bigint(20) DEFAULT '-1' COMMENT '本次交易后标签累计里程',
  `d_prov_min_fee` decimal(12,2) DEFAULT '-1.00' COMMENT '本省累计通行金额参考',
  `n_fee_spare1` bigint(30) DEFAULT '-1' COMMENT '计费协议保留字段1',
  `n_fee_spare2` bigint(30) DEFAULT '-1' COMMENT '计费协议保留字段2',
  `vc_fee_spare3` varchar(250) DEFAULT NULL COMMENT '计费协议保留字段3',
  `vc_fee_prov_begin_hex` varchar(16) DEFAULT NULL COMMENT '本省计费起点',
  `vc_trade_read_ciphertext` varchar(50) DEFAULT NULL COMMENT '本次交易读取的EF04加密摘要',
  `vc_trade_write_ciphertext` varchar(50) DEFAULT NULL COMMENT '本次交易生成的EF04加密摘要',
  `n_read_ciphertext_verify` tinyint(4) DEFAULT '-1' COMMENT '本次交易读取的EF04加密摘要验证结果 0-成功 1-失败',
  `n_rate_compute` tinyint(4) DEFAULT '-1' COMMENT '计费模块计费模式 0- 无拟合路径 1- 拟合路径 计费成功必填',
  `n_rate_fit_count` int(11) DEFAULT '-1' COMMENT '计费模块拟合点总数',
  `d_obu_prov_pay_fee_sum_before` decimal(12,2) DEFAULT '-1.00' COMMENT '本次交易前标签累计应收金额（省内）',
  `d_obu_prov_pay_fee_sum_after` decimal(12,2) DEFAULT '-1.00' COMMENT '本次交易后标签累计应收金额（省内）',
  `n_path_fit_flag` tinyint(4) DEFAULT '-1' COMMENT '0-未拟合或拟合成功，1-拟合失败，默认值-1',
  `vc_fee_calc_specials` varchar(150) DEFAULT NULL COMMENT '对应计费协议中计费特情值组合',
  `d_pay_fee_prov_sum_local` decimal(12,2) DEFAULT '-1.00' COMMENT 'ETC表示本省累计应收金额取整（含本门架应收金额）。CPC表示本省累计金额取整（含本门架计费金额），对应计费模块返回的payFeeProvSumLocal字段',
  `n_pcrsu_version` int(11) DEFAULT '-1' COMMENT 'PC-RSU接口协议版本号',
  `vc_gantry_pass_info_after` varchar(200) DEFAULT NULL COMMENT '本次写入的CPC卡EF02文件中过站信息',
  `n_cpc_fee_trade_result` tinyint(4) DEFAULT '-1' COMMENT 'CPC卡计费信息文件写入结果 0-成功 1-失败，默认值-1',
  `vc_fee_prov_ef04` varchar(100) DEFAULT NULL COMMENT '本次写入到CPC EF04中本省计费信息',
  `n_fit_prov_flag` tinyint(4) DEFAULT '-1' COMMENT 'CPC卡是否进行全省路径拟合的标识，0-未拟合，大于0的值省内自行定义拟合方式。默认值-1',
  `n_gantry_pass_count_before` int(11) DEFAULT '-1' COMMENT '读取CPC卡EF02文件中过站信息中门架数量。默认值-1',
  `vc_fee_prov_begin_hex_fit` varchar(16) DEFAULT '-1' COMMENT '拟合后的本省省界入口ETC门架HEX码',
  `ts_fee_prov_begin_time_fit` datetime DEFAULT NULL COMMENT '拟合后的本省省界入口ETC门架的通行时间',
  `vc_toll_interval_sign` varchar(100) DEFAULT NULL COMMENT '收费单元组合处理标识',
  `n_prov_min_fee_calc_mode` tinyint(4) DEFAULT '-1' COMMENT '本省累计通行金额计算方式',
  `d_obu_fee_sum_before` decimal(12,2) DEFAULT '-1.00' COMMENT '本次交易前标签累计实收金额 默认值-1',
  `d_obu_fee_sum_after` decimal(12,2) DEFAULT '-1.00' COMMENT '本次交易后标签累计实收金额 默认值-1',
  `n_fee_mileage` int(11) DEFAULT '-1' COMMENT '计费里程数',
  `n_update_result` tinyint(4) DEFAULT NULL COMMENT '更新过站信息结果 1-更新成功；2-更新失败',
  `d_fee_sum_local_after` decimal(12,2) DEFAULT '0.00' COMMENT 'CPC卡本省费用累计后金额',
  `ts_fee_prov_begin_time` datetime DEFAULT NULL COMMENT 'CPC本省省界入口ETC门架通行时间',
  `d_fee_sum_local_after_ef04` decimal(12,2) DEFAULT '-1.00' COMMENT 'CPC本省累计拆分金额',
  `d_last_gantry_fee_pass` decimal(12,2) DEFAULT '-1.00' COMMENT 'CPC EF02文件中上个门架的计费金额',
  `n_last_gantry_mile_pass` int(11) DEFAULT '-1' COMMENT 'CPC EF02文件中上个门架的计费里程',
  `ts_db_timestamp` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  PRIMARY KEY (`vc_trade_id`),
  KEY `idx_gantry_id` (`vc_gantry_id`),
  KEY `idx_hour_batch_no` (`vc_hour_batch_no`),
  KEY `idx_gantry_hex` (`vc_gantry_hex`),
  KEY `idx_n_station_id` (`n_station_id`),
  KEY `idx_trans_time` (`ts_trans_time`),
  KEY `idx_vehicle_plate` (`vc_vehicle_plate`),
  KEY `idx_pass_id` (`vc_pass_id`),
  KEY `idx_cpu_card_id` (`vc_cpu_card_id`),
  KEY `idx_gantry_jour_index` (`ts_trans_time`,`c_center_send_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


