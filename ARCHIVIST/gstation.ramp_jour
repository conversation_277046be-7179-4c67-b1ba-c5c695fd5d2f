CREATE TABLE IF NOT EXISTS `ramp_jour` (
  `n_ex_date` int(8) NOT NULL,
  `n_ex_sys_date` int(8) NOT NULL,
  `n_ex_time` int(6) NOT NULL,
  `n_ex_serial_no` int(6) NOT NULL,
  `n_ex_lane_id` int(6) NOT NULL,
  `c_ex_shift_code` char(1) COLLATE utf8_croatian_ci DEFAULT NULL,
  `n_ex_op_code` int(8) NOT NULL,
  `c_ex_category` char(1) COLLATE utf8_croatian_ci DEFAULT NULL,
  `c_ex_vehicle_class` char(1) COLLATE utf8_croatian_ci NOT NULL,
  `n_vehicle_user_type` int(8) NOT NULL,
  `n_vehicle_sign` int(8) NOT NULL,
  `c_sub_category` char(10) COLLATE utf8_croatian_ci DEFAULT NULL,
  `c_pay_way` char(1) COLLATE utf8_croatian_ci DEFAULT NULL,
  `d_fee` decimal(8,2) DEFAULT NULL,
  `d_leftFee` decimal(8,2) DEFAULT NULL,
  `vc_card_license` varchar(20) COLLATE utf8_croatian_ci DEFAULT NULL,
  `vc_iden_license` varchar(20) COLLATE utf8_croatian_ci DEFAULT NULL,
  `c_laneType` char(1) COLLATE utf8_croatian_ci DEFAULT NULL,
  `n_trade_mode` tinyint(4) DEFAULT NULL,
  `n_pass_valid_mode` tinyint(4) DEFAULT NULL,
  `n_ramp_date` int(8) NOT NULL,
  `n_ramp_sys_date` int(8) NOT NULL,
  `n_ramp_time` int(6) NOT NULL,
  `n_ramp_lane_id` int(6) NOT NULL,
  `n_ramp_serial_no` int(6) NOT NULL,
  `n_en_sys_date` int(8) NOT NULL,
  `n_en_time` int(6) NOT NULL,
  `n_en_station_id` int(6) NOT NULL,
  `vc_en_station_hex` varchar(8) COLLATE utf8_croatian_ci NOT NULL,
  `vc_issue_code` varchar(4) COLLATE utf8_croatian_ci DEFAULT NULL,
  `vc_card_id` varchar(16) COLLATE utf8_croatian_ci DEFAULT NULL,
  `c_station_send_flag` char(1) COLLATE utf8_croatian_ci DEFAULT NULL,
  `c_mot_send_flag` char(1) COLLATE utf8_croatian_ci DEFAULT NULL,
  `c_center_send_flag` char(1) COLLATE utf8_croatian_ci DEFAULT NULL,
  `ts_center_send_time` datetime DEFAULT NULL,
  `ts_mot_send_time` datetime DEFAULT NULL,
  `ts_db_timestamp` datetime DEFAULT CURRENT_TIMESTAMP,
  `d_weight` decimal(8,2) DEFAULT NULL,
  `c_ticket_type` char(1) COLLATE utf8_croatian_ci DEFAULT NULL,
  `n_axlecount` int(6) DEFAULT NULL,
  UNIQUE KEY `idx_ramp_jour` (`n_ex_date`,`n_ex_serial_no`,`n_ex_lane_id`) USING BTREE,
  KEY `idx_c_center_send_flag` (`c_center_send_flag`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_croatian_ci ROW_FORMAT=DYNAMIC;


