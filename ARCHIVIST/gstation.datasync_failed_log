CREATE TABLE IF NOT EXISTS `datasync_failed_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '处理失败日志记录ID',
  `n_station_id` int(4) NOT NULL COMMENT '站ID',
  `vc_source_id` varchar(21) COLLATE utf8mb4_bin NOT NULL COMMENT '处理失败源车道ID/门架ID',
  `vc_table` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '数据表',
  `vc_cause` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '处理失败原因',
  `ts_timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据处理时间',
  `t_data` text COLLATE utf8mb4_bin NOT NULL COMMENT '处理失败数据内容',
  `c_center_send_flag` tinyint(4) DEFAULT '1' COMMENT '省中心发送标志',
  PRIMARY KEY (`id`),
  KEY `idx_station_id` (`n_station_id`),
  KEY `idx_timestamp_sendflag` (`ts_timestamp`,`c_center_send_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


