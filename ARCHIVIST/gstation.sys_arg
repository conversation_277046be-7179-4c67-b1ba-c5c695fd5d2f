CREATE TABLE IF NOT EXISTS `sys_arg` (
  `n_station_id` decimal(8,0) NOT NULL,
  `vc_station_name` varchar(30) DEFAULT NULL,
  `n_road_id` decimal(4,0) DEFAULT NULL,
  `vc_road_name` varchar(30) DEFAULT NULL,
  `n_company_id` decimal(8,0) DEFAULT NULL,
  `vc_company_name` varchar(30) DEFAULT NULL,
  `n_owner_id` decimal(8,0) DEFAULT NULL,
  `vc_owner_name` varchar(30) DEFAULT NULL,
  `n_last_init_date` decimal(8,0) DEFAULT NULL,
  `n_init_date` decimal(8,0) DEFAULT NULL,
  `c_init` char(1) DEFAULT NULL,
  `n_open_date` decimal(8,0) DEFAULT NULL,
  `n_lane_cnt` decimal(2,0) DEFAULT NULL,
  `c_switch_code` char(1) DEFAULT NULL,
  `c_standards` char(1) DEFAULT NULL,
  PRIMARY KEY (`n_station_id`),
  UNIQUE KEY `PK__sys_arg__1920BF5C` (`n_station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


