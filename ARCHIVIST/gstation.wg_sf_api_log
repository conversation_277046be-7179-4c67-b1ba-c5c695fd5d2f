CREATE TABLE IF NOT EXISTS `wg_sf_api_log` (
  `vc_log_id` varchar(36) NOT NULL COMMENT '日志编号 UUID',
  `dt_accept_time` datetime NOT NULL COMMENT '接受时间',
  `dt_return_time` datetime DEFAULT NULL COMMENT '返回时间',
  `n_time_consume` decimal(10,0) DEFAULT NULL COMMENT '处理耗时',
  `n_station_id` decimal(4,0) DEFAULT NULL COMMENT '收费站编码',
  `vc_point_num` varchar(20) DEFAULT NULL COMMENT '识别点编号',
  `c_point_type` char(1) DEFAULT NULL COMMENT '识别点类型 0-标识点；1-省界虚拟站；2-收费站',
  `vc_method` varchar(128) DEFAULT NULL COMMENT '接口名称',
  `vc_ip` varchar(15) DEFAULT NULL COMMENT '用户IP',
  `vc_request_pkg` varchar(2000) DEFAULT NULL COMMENT '请求包内容',
  `vc_response_pkg` varchar(2000) DEFAULT NULL COMMENT '返回包内容',
  `vc_result_code` varchar(5) DEFAULT NULL COMMENT '返回代码',
  `vc_result_msg` varchar(256) DEFAULT NULL COMMENT '返回信息',
  KEY `ix_api_log_accept` (`dt_accept_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;