CREATE TABLE IF NOT EXISTS `point_status` (
  `vc_point_num` varchar(20) NOT NULL COMMENT '识别点编号',
  `n_date` decimal(8,0) NOT NULL COMMENT '日期',
  `n_time` decimal(6,0) NOT NULL COMMENT '时间',
  `c_point_type` char(1) NOT NULL COMMENT '识别点类型 0-标识点；1-省界虚拟站；2-收费站',
  `n_station_id` decimal(4,0) NOT NULL COMMENT '收费站编码',
  `n_osk_flow_cnt` decimal(8,0) NOT NULL DEFAULT '0' COMMENT '积压图片流水数',
  `n_osk_image_cnt` decimal(8,0) NOT NULL DEFAULT '0' COMMENT '积压图片数',
  PRIMARY KEY (`vc_point_num`,`n_date`,`n_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


