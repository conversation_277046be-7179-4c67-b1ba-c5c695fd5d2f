CREATE TABLE IF NOT EXISTS `lane_status_info` (
  `n_lane_id` int(8) NOT NULL COMMENT '车道代码',
  `n_date` int(8) NOT NULL COMMENT '日期',
  `n_time` int(6) NOT NULL COMMENT '时间',
  `n_serial_no` int(8) DEFAULT NULL COMMENT '流水号',
  `n_record_no` int(8) NOT NULL COMMENT '索引序号',
  `n_op_code` int(8) DEFAULT NULL COMMENT '收费员代码',
  `c_shift_code` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '工班代码',
  `n_curr_fare` int(10) DEFAULT NULL COMMENT '费率版本',
  `n_station_id` int(8) DEFAULT NULL COMMENT '收费站编码',
  `c_lane_type` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车道类型',
  `c_login` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '工班登录',
  `n_shift_date` int(8) DEFAULT NULL COMMENT '登录日期',
  `vc_system_ver` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '系统软件版本号',
  `n_bak_fare` int(10) DEFAULT NULL COMMENT '第一套备用版本号',
  `n_black_num` int(8) DEFAULT NULL COMMENT '生效黑名单数量',
  `n_white_num` int(8) DEFAULT NULL COMMENT '生效白名单数量',
  `n_grey_num` int(8) DEFAULT NULL COMMENT '生效灰名单数量',
  `c_station_link` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '与收费站通讯状态',
  `c_center_link` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '与总中心通讯状态',
  `n_station_jour` int(6) DEFAULT NULL COMMENT '至收费站积压流水数',
  `n_center_jour` int(6) DEFAULT NULL COMMENT '至总中心积压流水数',
  `c_switch_a` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '路标故障信息',
  `c_switch_b` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '复合卡故障信息',
  `c_switch_c` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '复合卡状态信息',
  `c_switch_d` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车道状态信息',
  `c_switch_e` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留开关',
  `c_switch_f` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留开关',
  `c_send_flag1` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发送标志1',
  `c_send_flag2` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发送标志2',
  `c_send_flag3` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发送标志3',
  `c_black_fullver` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '黑名单全量版本',
  `c_black_incver` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '黑名单增量版本',
  `c_white_fullver` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '白名单全量版本',
  `c_white_incver` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '白名单增量版本',
  `c_gray_fullver` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '灰名单全量版本',
  `c_gray_incver` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '灰名单增量版本',
  `vc_license_gray_fullver` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车牌灰名单全量版本',
  `vc_license_gray_incver` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车牌灰名单增量版本',
  `vc_obu_black_fullver` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'OBU黑名单全量版本',
  `vc_obu_black_incver` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'OBU黑名单增量版本',
  `vc_cpc_black_fullver` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'CPC灰名单全量版本',
  `vc_cpc_black_incver` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'CPC灰名单增量版本',
  `vc_module_ver` varchar(450) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '计费模块版本号',
  `vc_param_ver` varchar(450) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '计费参数版本号',
  `vc_nation_module_ver` varchar(11) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '最短路径计费模块版本号',
  `vc_spc_rate_ver` varchar(40) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '最短路径计费参数版本号',
  `n_lane_status` int(11) DEFAULT NULL COMMENT '车道状态',
  `vc_rsu_status` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'RSU状态',
  `vc_reader_status` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '读卡器状态',
  `vc_vplr_status` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车牌识别状态',
  `vc_vehdetec_status` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车检器状态',
  `vc_light_detec_status` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '光栅状态',
  `vc_hdv_status` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车道摄像机状态',
  `n_feeboard_status` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '费额显示器状态',
  `n_hintsboard_status` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '信息提示屏状态',
  `n_tradfficlight_status` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '通行信号灯状态',
  `n_infoboard_status` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'ETC情报板',
  `n_os_ver` varchar(40) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '操作系统版本',
  `vc_lanecomm_ver` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车道通讯版本号',
  `vc_actualcharge_ver` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '动态费率版本号',
  `vc_devices_status` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车道设备驱动状态',
  `vc_devices_type` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车道设备类型',
  `n_curr_fare_num` int(8) DEFAULT NULL COMMENT '当前费率数量',
  `n_bak_fare_num` int(8) DEFAULT NULL COMMENT '备用费率数量',
  `vc_hsreader_ver` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '读写器动态库版本号',
  `vc_rapi_ver` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '读写器厂家动态库版本号',
  `vc_hsweight_ver` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '计重动态库版本号',
  `vc_hsencryption_ver` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '圈存加密动态库版本',
  `vc_hsetcdeposit_ver` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '圈存动态库版本号',
  `vc_hseasypay_ver` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '移动支付动态库版本',
  `vc_etcrsu_ver` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '天线动态库版本号',
  `n_temp1` int(8) DEFAULT NULL COMMENT '预留',
  `c_temp1` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '预留',
  `n_temp2` int(8) DEFAULT NULL COMMENT '预留',
  `c_temp2` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '预留',
  `n_temp3` int(8) DEFAULT NULL COMMENT '预留',
  `c_temp3` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '预留',
  `n_temp4` int(8) DEFAULT NULL COMMENT '预留',
  `c_temp4` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '预留',
  `c_ip` varchar(16) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'c_ip',
  `c_mot_send_flag` tinyint(4) DEFAULT '0' COMMENT '部中心发送标志',
  `c_center_send_flag` tinyint(4) DEFAULT '0' COMMENT '省中心发送标志',
  `vc_monitor_a` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '监测信息A',
  `vc_monitor_b` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '监测信息B',
  `vc_monitor_c` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '监测信息C',
  `vc_monitor_d` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '监测信息D',
  `vc_monitor_e` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '监测信息E',
  `vc_monitor_f` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '监测信息F',
  UNIQUE KEY `idx_n_lane_id_uniq` (`n_lane_id`,`n_date`,`n_record_no`,`n_time`),
  KEY `idx_lane_status_info_1_index` (`n_record_no`),
  KEY `idx_lane_status_info_2_index` (`c_center_send_flag`,`n_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


