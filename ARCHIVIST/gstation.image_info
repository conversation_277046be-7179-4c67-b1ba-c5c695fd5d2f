CREATE TABLE IF NOT EXISTS `image_info` (
  `vc_point_num` varchar(20) NOT NULL COMMENT '识别点编号',
  `n_date` decimal(8,0) NOT NULL COMMENT '拍摄日期',
  `n_time` decimal(6,0) NOT NULL COMMENT '拍摄时间',
  `vc_sequence_no` varchar(3) NOT NULL COMMENT '顺序码',
  `vc_image_path` varchar(256) NOT NULL COMMENT '图片相对路径',
  `vc_license_image_path` varchar(256) NOT NULL COMMENT '车牌图片相对路径',
  `vc_image_id` varchar(256) DEFAULT NULL COMMENT '图片在图片服务器中的ID',
  `vc_license_image_id` varchar(256) DEFAULT NULL COMMENT '车牌图片在图片服务器中的ID',
  `c_status` char(1) NOT NULL DEFAULT '0' COMMENT '图片状态 0-正常，1-删除',
  PRIMARY KEY (`vc_point_num`,`n_date`,`n_time`,`vc_sequence_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


