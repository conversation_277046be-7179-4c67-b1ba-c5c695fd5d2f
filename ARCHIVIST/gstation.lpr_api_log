CREATE TABLE IF NOT EXISTS `lpr_api_log` (
  `vc_log_id` varchar(36) COLLATE utf8mb4_bin NOT NULL COMMENT '日志编号',
  `dt_accept_time` datetime NOT NULL COMMENT '接受时间',
  `dt_return_time` datetime DEFAULT NULL COMMENT '返回时间',
  `n_time_consume` int(10) DEFAULT NULL COMMENT '处理耗时',
  `n_station_id` int(4) DEFAULT NULL COMMENT '收费站编码',
  `vc_gantry_id` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '识别点编号',
  `n_point_type` int(1) NOT NULL COMMENT '识别点类型',
  `vc_method` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '接口名称',
  `vc_ip` varchar(15) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '用户IP',
  `vc_request_pkg` varchar(2000) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求包内容',
  `vc_response_pkg` varchar(2000) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '返回包内容',
  `vc_result_code` varchar(5) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '返回代码',
  `vc_result_msg` varchar(256) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '返回信息',
  PRIMARY KEY (`vc_log_id`),
  KEY `idx_station_id` (`n_station_id`),
  KEY `idx_gantry_id` (`vc_gantry_id`),
  KEY `idx_accept_time` (`dt_accept_time`),
  KEY `idx_return_time` (`dt_return_time`),
  KEY `idx_time_consume` (`n_time_consume`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


