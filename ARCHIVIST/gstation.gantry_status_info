CREATE TABLE IF NOT EXISTS `gantry_status_info` (
  `vc_gantry_id` varchar(19) COLLATE utf8mb4_bin NOT NULL COMMENT '门架编号',
  `n_sect_gantry_id` int(5) NOT NULL COMMENT '省内门架编号',
  `vc_front_run_state_id` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '前端状态流水号',
  `ts_front_state_time` timestamp NOT NULL DEFAULT '2019-01-01 01:00:00' COMMENT '前端状态数据生成时间',
  `vc_front_rate_version` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '计费模块和计费参数版本号',
  `vc_front_param_version` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '前端运行参数版本号',
  `vc_front_sys_version` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '门架前端控制机操作系统软件版本',
  `vc_front_soft_version` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '前端软件版本号',
  `vc_front_soft_state` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '前端软件运行状态',
  `n_front_over_trade_count` int(11) NOT NULL COMMENT '前端积压通行流水数',
  `n_front_trans_err_trade_count` int(11) NOT NULL COMMENT '前端传输异常通行流水数',
  `n_front_over_veh_data_count` int(11) DEFAULT NULL COMMENT '前端积压牌识流水数',
  `n_front_trans_err_veh_data_count` int(11) DEFAULT NULL COMMENT '前端传输异常牌识流水数',
  `d_front_load_average` decimal(5,2) DEFAULT NULL COMMENT '前端硬件资源平均负载（5分钟）',
  `vc_front_disk_data_total_size` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '前端数据盘总容量',
  `vc_front_disk_data_left_size` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '前端数据盘剩余容量',
  `vc_front_disk_run_total_size` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '前端运行盘总容量',
  `vc_front_disk_run_left_size` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '前端运行盘剩余容量',
  `d_front_cpu_rate` decimal(5,2) DEFAULT NULL COMMENT '前端cpu使用率',
  `d_front_memory_rate` decimal(5,2) NOT NULL COMMENT '前端物理内存使用率',
  `n_front_beidou_state` tinyint(4) NOT NULL COMMENT '前端北斗授时服务状态',
  `vc_charge_unit_id` varchar(19) COLLATE utf8mb4_bin NOT NULL COMMENT '门架后台编码',
  `vc_back_run_state_id` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '门架后台状态流水号',
  `ts_back_state_time` timestamp NOT NULL DEFAULT '2019-01-01 01:00:00' COMMENT '门架后台状态数据生成时间',
  `vc_back_sys_version` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '门架后台服务器操作系统软件版本',
  `vc_back_db_version` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '门架后台服务器数据库系统软件版本',
  `vc_back_param_version` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '后台运行参数版本号',
  `vc_back_soft_version` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '后台软件版本号',
  `n_back_soft_state` tinyint(4) NOT NULL COMMENT '后台软件运行状态',
  `n_backovertradecount` int(11) NOT NULL COMMENT '后台积压通行流水数',
  `n_back_trans_err_trade_count` int(11) NOT NULL COMMENT '后台传输异常通行流水数',
  `n_back_over_veh_data_count` int(11) NOT NULL COMMENT '后台积压牌识流水数',
  `n_back_trans_err_veh_data_count` int(11) NOT NULL COMMENT '后台传输异常牌识流水数',
  `n_back_over_veh_pic_count` int(11) NOT NULL COMMENT '后台积压牌识图片数',
  `n_back_trans_err_veh_pic_count` int(11) NOT NULL COMMENT '后台传输异常牌识图片数',
  `n_back_computer_state` tinyint(4) DEFAULT NULL COMMENT '后台工作主机状态',
  `d_back_load_average` decimal(5,2) DEFAULT NULL COMMENT '后台硬件资源平均负载（5分钟）',
  `vc_back_disk_data_total_size` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '后台数据盘总容量',
  `vc_back_disk_data_left_size` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '后台数据盘剩余容量',
  `vc_back_disk_run_total_size` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '后台运行盘总容量',
  `vc_back_disk_run_left_size` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '后台运行盘剩余容量',
  `d_back_cpu_rate` decimal(5,2) DEFAULT NULL COMMENT '后台cpu使用率',
  `d_back_memory_rate` decimal(5,2) DEFAULT NULL COMMENT '后台物理内存使用率',
  `n_back_beidou_state` tinyint(4) DEFAULT NULL COMMENT '后台北斗授时服务状态',
  `vc_vehicle_detector_status` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车检器状态',
  `vc_vehicle_detector_version` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车检器软件版本号',
  `vc_weather_detector_status` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '气象检测设备状态',
  `vc_weather_detector_version` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '气象检测设备软件版本号',
  `vc_class_detector_status` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车型检测器状态',
  `vc_class_detector_version` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车型检测器软件版本号',
  `vc_load_detector_status` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '载重检测器状态',
  `vc_load_detector_version` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '载重检测器软件版本号',
  `vc_temp_controller_status` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '温控设备状态',
  `vc_power_controller_status` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '供电设备状态',
  `ts_create_time` timestamp NOT NULL DEFAULT '2019-01-01 01:00:00' COMMENT '数据生成时间',
  `c_mot_send_flag` tinyint(4) DEFAULT '0' COMMENT '部中心发送标志',
  `c_center_send_flag` tinyint(4) DEFAULT '0' COMMENT '省中心发送标志',
  `c_midcenter_send_flag` tinyint(4) DEFAULT '0' COMMENT '路段中心发送标志',
  `c_subcenter_send_flag` tinyint(4) DEFAULT '0' COMMENT '分中心发送标志',
  `c_station_send_flag` tinyint(4) DEFAULT '0' COMMENT '收费站发送标志',
  KEY `idx_gantry_id` (`vc_gantry_id`),
  KEY `idx_create_time` (`ts_create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


