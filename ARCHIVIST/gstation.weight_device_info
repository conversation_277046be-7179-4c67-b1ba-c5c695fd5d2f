CREATE TABLE IF NOT EXISTS `weight_device_info` (
  `n_lane_num` int(7) DEFAULT NULL COMMENT '车道编号',
  `n_device_num` int(3) NOT NULL COMMENT '称重设备的编号',
  `n_vendor_code` tinyint(4) NOT NULL COMMENT '厂商代(1-托利多；2-新大陆；3-万集；4-四方；5-德鲁泰；6-金钟)',
  `vc_device_model` varchar(8) NOT NULL COMMENT '设备型号',
  `vc_device_version` varchar(8) NOT NULL COMMENT '设备版本号',
  `n_accuracy` tinyint(4) NOT NULL COMMENT '设备精度要求(1) 整车式称重精度OIML等级：1～4；(2) 轴重式称重精度（在没有明显加减速的前提下）：车速在0～20km/h时，最大称重精度：±0～±255',
  `n_rated_load` int(3) NOT NULL COMMENT '单轴额定荷载:0－255(单位：吨)',
  `n_overload_ability` int(3) NOT NULL COMMENT '单轴过载能力:0－255(单位：吨)',
  UNIQUE KEY `IDX_LANE_NUM_IDX` (`n_lane_num`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='称重设备信息';


