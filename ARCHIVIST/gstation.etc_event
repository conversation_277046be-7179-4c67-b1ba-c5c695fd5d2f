CREATE TABLE IF NOT EXISTS `etc_event` (
  `n_id` int(11) NOT NULL,
  `n_date` int(11) NOT NULL,
  `n_time` int(11) DEFAULT NULL,
  `n_shift_date` int(11) DEFAULT NULL,
  `c_shift_code` char(1) COLLATE utf8_croatian_ci DEFAULT NULL,
  `n_op_code` int(11) DEFAULT NULL,
  `n_lane_id` int(11) NOT NULL,
  `n_serial_no` int(11) DEFAULT NULL,
  `c_obu_id` char(16) COLLATE utf8_croatian_ci DEFAULT NULL,
  `c_cpu_id` char(16) COLLATE utf8_croatian_ci DEFAULT NULL,
  `c_license` char(12) COLLATE utf8_croatian_ci DEFAULT NULL,
  `n_event_type` int(11) DEFAULT NULL,
  `vc_event_type` varchar(31) COLLATE utf8_croatian_ci DEFAULT NULL,
  `c_sub_type` char(1) COLLATE utf8_croatian_ci DEFAULT NULL,
  `vc_sub_type` varchar(31) COLLATE utf8_croatian_ci DEFAULT NULL,
  `c_send_flag1` char(1) COLLATE utf8_croatian_ci DEFAULT NULL,
  `c_send_flag2` char(1) COLLATE utf8_croatian_ci DEFAULT NULL,
  UNIQUE KEY `idx_etc_event` (`n_id`,`n_date`,`n_lane_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_croatian_ci ROW_FORMAT=DYNAMIC;


