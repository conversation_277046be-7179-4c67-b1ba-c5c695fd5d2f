CREATE TABLE IF NOT EXISTS `antennal_info_list` (
  `vc_gantry_id` varchar(19) COLLATE utf8mb4_bin NOT NULL COMMENT '门架编号',
  `n_sect_gantry_id` int(5) NOT NULL COMMENT '省内门架编号',
  `ts_create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据生成时间',
  `vc_antenna_id` varchar(10) COLLATE utf8mb4_bin NOT NULL COMMENT '天线头编号',
  `vc_status` varchar(10) COLLATE utf8mb4_bin NOT NULL COMMENT 'RSU天线头的状态信息',
  `vc_antenna_temperature` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'RSU天线头温度',
  KEY `idx_gantry_id` (`vc_gantry_id`),
  KEY `idx_sect_gantry_id` (`n_sect_gantry_id`),
  KEY `idx_create_time` (`ts_create_time`),
  KEY `idx_antenna_id` (`vc_antenna_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


