CREATE TABLE IF NOT EXISTS `epidemic_area_intercept_list` (
  `vc_id` varchar(32) NOT NULL COMMENT '主键,UUID',
  `n_in_date` int(11) NOT NULL COMMENT '入单日期',
  `n_in_time` int(11) NOT NULL COMMENT '入单时间',
  `n_valid_date` int(11) NOT NULL COMMENT '生效日期',
  `n_invalid_date` int(11) NOT NULL COMMENT '失效日期',
  `n_type` tinyint(3) NOT NULL COMMENT '类型:0-入口站，1-车牌，2-车牌属地，3-行政区域',
  `vc_intercept_id` varchar(4000) NOT NULL COMMENT '拦截ID:多个ID，用竖线隔开',
  `vc_comment` varchar(500) DEFAULT NULL COMMENT '备注',
  `n_level` tinyint(4) NOT NULL DEFAULT '1' COMMENT '风险等级:1-低风险,2-中风险,3-高风险',
  `n_source` tinyint(4) NOT NULL DEFAULT '0' COMMENT '数据来源:0-收费站填报,1-省中心下发',
  PRIMARY KEY (`vc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='疫区拦截名单';


