CREATE TABLE IF NOT EXISTS `hex_code_mapping` (
  `vc_hex_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT 'HEX编码',
  `vc_code_type` varchar(5) COLLATE utf8mb4_bin NOT NULL COMMENT '编码类型',
  `vc_target_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '浙江编码',
  PRIMARY KEY (`vc_hex_id`,`vc_code_type`),
  KEY `idx_target_id` (`vc_target_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


