CREATE TABLE IF NOT EXISTS `weight_jour` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT,
  `n_lane_num` int(7) NOT NULL COMMENT '称重车道编号',
  `vc_license_num` varchar(20) NOT NULL COMMENT '车牌号码',
  `n_license_color` tinyint(4) NOT NULL COMMENT '车牌颜色（0-蓝，1-黄，2-黑，3-白，4-渐变绿，5-黄绿双拼，6-蓝白渐变，9-未确定）',
  `n_device_num` int(3) NOT NULL COMMENT '称重检测设备编号',
  `n_date` int(8) NOT NULL COMMENT '称重日期',
  `n_time` int(6) NOT NULL COMMENT '称重时间',
  `vc_obu_id` varchar(20) NOT NULL DEFAULT '0' COMMENT 'obu编号',
  `n_vehicle_class` int(2) DEFAULT NULL COMMENT '入口车型(1-一型客车,2-二型客车,3-三型客车,4-四型客车,11-一型货车,12-二型货车,13-三型货车,14-四型货车,15-五型货车,16-六型货车,21-一型专项作业车,22-二型专项作业车,23-三型专项作业车,24-四型专项作业车,25-五型专项作业车,26-六型专项作业车),',
  `n_over_length` int(8) DEFAULT NULL COMMENT '入口超长量',
  `n_over_width` int(8) DEFAULT NULL COMMENT '入口超宽量',
  `n_over_height` int(8) DEFAULT NULL COMMENT '入口超高量',
  `n_total_weight` int(8) DEFAULT NULL COMMENT '车辆总重, 权值(0.01吨) ；单位：千克：0～999999',
  `n_weight_limit` int(8) DEFAULT NULL COMMENT '车货限重, 权值(0.01吨) ；单位：千克：0～999999',
  `n_over_weight` int(8) DEFAULT NULL COMMENT '超限量, 权值(0.01吨) ；单位：千克：0～999999',
  `n_weight_limit_rate` int(4) DEFAULT NULL COMMENT '超限量 ; xx%',
  `n_is_vehicle_over_size` tinyint(4) DEFAULT NULL COMMENT '是否大件运输车辆, 0-是；1-否',
  `n_is_permit_pass` tinyint(4) DEFAULT NULL COMMENT '是否准许通行',
  `n_speed` int(8) NOT NULL COMMENT '速度0～65536；(权值0.1km/h，读数与权值相乘为实际值)',
  `n_acceleration` int(4) NOT NULL COMMENT '加速度-128～127；(权值1km/h，读数与权值相乘为实际值)',
  `n_axle_num` int(4) NOT NULL COMMENT '轴数',
  `n_status` tinyint(4) DEFAULT '0' COMMENT '是否已同步',
  `n_front_image_size` int(6) DEFAULT NULL COMMENT '车辆正面照长度, 单位字节',
  `vc_front_image` varchar(512) DEFAULT NULL COMMENT '车辆正面照',
  `n_front45_image_size` int(6) DEFAULT NULL COMMENT '车辆侧面照长度',
  `vc_front45_image` varchar(512) DEFAULT NULL COMMENT '车辆侧面照',
  `n_behind_image_size` int(6) DEFAULT NULL COMMENT '车辆尾部照长度',
  `vc_behind_image` varchar(512) DEFAULT NULL COMMENT '车辆尾部照',
  `n_license_image_size` int(6) DEFAULT NULL COMMENT '车辆号牌图片长度',
  `vc_license_image` varchar(512) DEFAULT NULL COMMENT '车辆号牌图片',
  `n_vehicle_video_size` int(6) DEFAULT NULL COMMENT '入口视频长度',
  `vc_vehicle_video` varchar(512) DEFAULT NULL COMMENT '入口视屏',
  `vc_equip_code` varchar(32) DEFAULT NULL COMMENT '称重检测设备编号',
  `n_is_black_vehicle` int(2) DEFAULT NULL COMMENT '是否为黑名单车辆(1.是;2.否)',
  `n_obu_user_type` int(2) DEFAULT NULL COMMENT '车辆用户类型(1.个人，2.单位)',
  `vc_bulk_cert` varchar(32) DEFAULT NULL COMMENT '大件运输许可证号',
  `vc_pass_id` varchar(50) DEFAULT NULL,
  `n_obu_total_weight` int(8) DEFAULT NULL COMMENT 'OBU车辆总质量',
  `n_obu_maintenance_weight` int(8) DEFAULT NULL COMMENT 'OBU整备质量',
  `n_obu_permitted_tow_weight` int(8) DEFAULT NULL COMMENT 'OBU车辆准牵引总质量',
  `n_obu_permitted_weight` int(8) DEFAULT NULL COMMENT 'OBU车辆核定载质量',
  `n_obu_length` int(8) DEFAULT NULL COMMENT 'OBU车辆长度',
  `n_obu_weight` int(8) DEFAULT NULL COMMENT 'OBU车辆宽度',
  `n_obu_hegiht` int(8) DEFAULT NULL COMMENT 'OBU车辆高度',
  `dt_gmt_create` datetime DEFAULT NULL COMMENT '入库时间',
  `vc_trade_id` varchar(50) DEFAULT NULL COMMENT '计费交易编号',
  `n_upload_status` int(8) DEFAULT '0' COMMENT '同步标志,默认0-未同步',
  `n_pass_check` tinyint(4) DEFAULT '0' COMMENT '是否已匹配entry表(0-未匹配1-已匹配)默认0',
  `n_vehicle_usertype` int(10) DEFAULT NULL COMMENT '车辆用户类型',
  `c_category` char(1) DEFAULT NULL COMMENT '客货车类型',
  `dt_match_passid` datetime DEFAULT NULL COMMENT 'passid匹配时间',
  `ts_center_send_time` datetime DEFAULT NULL,
  `vc_en_axle_type` varchar(128) DEFAULT NULL COMMENT '入口车轴类型',
  `vc_etc_license` varchar(20) DEFAULT NULL COMMENT 'etc车牌',
  `n_etc_license_color` varchar(20) DEFAULT '0' COMMENT 'etc车牌颜色',
  `n_etc_vehicle_class` varchar(20) DEFAULT '0' COMMENT '发行车型',
  `n_match_status` varchar(20) DEFAULT '0' COMMENT '匹配状态:0-未匹配 1-仅牌识  2-仅标签  3-牌签一致,无需修正  4-牌签一致,根据ETC修正  5-牌签不一致',
  `vc_obu_mac` varchar(20) DEFAULT NULL COMMENT '标签mac',
  `vc_card_id` varchar(50) DEFAULT NULL COMMENT 'etc卡号',
  `vc_obu_status` varchar(20) DEFAULT NULL COMMENT '标签状态',
  `vc_issuer_id` varchar(20) DEFAULT NULL COMMENT '发行id',
  PRIMARY KEY (`id`),
  KEY `IDX_LICENSE_DATA_IDX` (`n_lane_num`,`vc_license_num`,`n_license_color`,`n_device_num`,`n_date`,`n_time`) USING BTREE,
  KEY `IDX_LICENSE_DATA_IDX1` (`vc_license_num`,`n_license_color`,`n_date`,`n_time`),
  KEY `weight_jour_n_lane_num_IDX` (`n_lane_num`,`vc_license_num`) USING BTREE,
  KEY `weight_jour_vc_pass_id_IDX` (`vc_pass_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8860704 DEFAULT CHARSET=utf8 COMMENT='称重信息';


