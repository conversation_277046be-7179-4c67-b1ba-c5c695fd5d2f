CREATE TABLE IF NOT EXISTS `task_config` (
  `vc_program_no` varchar(3) NOT NULL,
  `vc_task_no` varchar(8) NOT NULL,
  `vc_task_name` varchar(50) NOT NULL,
  `c_task_type` char(1) NOT NULL,
  `vc_base_path` varchar(255) DEFAULT NULL,
  `n_last_deal_date` decimal(8,0) DEFAULT NULL,
  `n_last_deal_time` decimal(6,0) DEFAULT NULL,
  `vc_trigger` varchar(255) DEFAULT NULL,
  `vc_task_class` varchar(100) DEFAULT NULL,
  `vc_delay` varchar(50) DEFAULT NULL,
  `c_switch` char(1) DEFAULT NULL,
  KEY `IX_task_config` (`vc_program_no`,`vc_task_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


