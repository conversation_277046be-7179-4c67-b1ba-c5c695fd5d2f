CREATE TABLE IF NOT EXISTS `epidemic_area_vehicle` (
  `vc_trade_id` varchar(38) CHARACTER SET utf8mb4 NOT NULL COMMENT '出口交易代码',
  `n_ex_lane_id` int(6) NOT NULL COMMENT '出口车道代码',
  `en_time` datetime DEFAULT NULL COMMENT '入口时间',
  `ex_time` datetime DEFAULT NULL COMMENT '出口时间',
  `c_ex_license` varchar(20) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '出口车牌号码',
  `c_category` char(1) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '车辆类别',
  `c_ex_vehicle_class` char(1) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '出口车型',
  `vc_exhex` varchar(10) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '入口收费站Hex',
  `vc_enhex` varchar(10) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '出口收费站Hex',
  `n_epidemic_type` tinyint(4) NOT NULL COMMENT '类型:0-入口站，1-车牌，2-车牌属地，3-区域',
  `n_epidemic_level` tinyint(4) NOT NULL DEFAULT '1' COMMENT '风险等级:1-低风险,2-中风险,3-高风险',
  `n_epidemic_source` tinyint(4) NOT NULL DEFAULT '0' COMMENT '数据来源:0-收费站填报,1-省中心下发',
  `c_mot_send_flag` tinyint(4) DEFAULT '0' COMMENT '部中心发送标志',
  `ts_mot_send_time` datetime DEFAULT NULL COMMENT '发送部站接口时间',
  `c_center_send_flag` tinyint(4) DEFAULT '0' COMMENT '省中心发送标志',
  `ts_center_send_time` datetime DEFAULT NULL COMMENT '发送省中心kafka时间',
  `c_road_send_flag` tinyint(4) DEFAULT '1' COMMENT '路中心发送标志',
  `ts_road_send_time` datetime DEFAULT NULL COMMENT '路中心发送时间',
  `ts_created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  PRIMARY KEY (`vc_trade_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


