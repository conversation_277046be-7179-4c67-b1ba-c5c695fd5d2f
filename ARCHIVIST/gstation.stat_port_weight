CREATE TABLE IF NOT EXISTS `stat_port_weight` (
  `n_date` int(8) NOT NULL COMMENT '统计日期',
  `n_station_id` int(4) DEFAULT NULL COMMENT '收费站编号',
  `n_port_count` int(8) DEFAULT NULL COMMENT '港口称重车辆数',
  `n_port_overweight_count` int(8) DEFAULT NULL COMMENT '港区超限车辆数',
  `n_station_count` int(8) DEFAULT NULL COMMENT '收费站货车流量',
  `n_overweight_count` int(8) DEFAULT NULL COMMENT '收费站劝返车辆数',
  `n_weight_port_lane` int(8) DEFAULT NULL COMMENT '港口过来车辆，但是上高速使用现场称重',
  `n_weight_port` int(8) DEFAULT NULL COMMENT '港口过来车辆。使用港口重量上高速',
  `n_overweight_port_lane_count` int(8) DEFAULT NULL COMMENT '港口过来超限，高速现场也超限',
  `n_overweight_lane_count` int(8) DEFAULT NULL COMMENT '港口过来未超限，高速现场超限',
  `dt_gmt_create` datetime DEFAULT NULL,
  `c_center_send_flag` tinyint(2) DEFAULT '0' COMMENT '上传中心标志位0-未上传（DEFAULT）1-已上传',
  `ts_center_send_time` datetime DEFAULT NULL,
  PRIMARY KEY (`n_date`) USING BTREE,
  KEY `idx_c_center_send_flag` (`c_center_send_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


