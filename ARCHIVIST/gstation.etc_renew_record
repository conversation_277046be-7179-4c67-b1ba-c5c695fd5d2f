CREATE TABLE IF NOT EXISTS `etc_renew_record` (
  `vc_id` varchar(64) NOT NULL COMMENT '主键:n_date日期(8位)+n_time时间(6位)+车道号(6位)+标签mac(8位)+标签号(16位)',
  `n_lane_id` int(11) NOT NULL COMMENT '省内车道编码',
  `n_date` int(11) NOT NULL COMMENT '系统日期',
  `n_time` int(11) NOT NULL COMMENT '系统时间',
  `vc_mac_id` varchar(16) NOT NULL COMMENT '标签mac',
  `vc_obu_id` varchar(16) NOT NULL COMMENT '标签id',
  `vc_obu_license` varchar(16) NOT NULL COMMENT '标签车牌：车牌_颜色',
  `vc_card_id` varchar(16) NOT NULL COMMENT '卡号',
  `vc_card_license` varchar(16) NOT NULL COMMENT '卡内车牌：车牌_颜色',
  `n_card_expired_before` int(11) NOT NULL COMMENT '卡片续期前失效日期',
  `vc_card_balance_before` varchar(16) NOT NULL COMMENT '卡内续期前余额',
  `n_obu_expired_before` int(11) NOT NULL COMMENT '标签续期前失效日期',
  `n_func_type` tinyint(4) NOT NULL COMMENT '业务功能类型  1-OBU续期 2-ETC卡续期 3-ETC卡圈存 4-测试OBU续期 5-测试ETC卡续期 6-测试ETC卡圈存',
  `n_func_status` tinyint(4) NOT NULL COMMENT '业务功能执行状态  0-业务功能执行成功 其他值为失败 错误代码： 芸车道为PLC上报状态码 传统车道为接口返回值',
  `n_func_used_time` int(11) NOT NULL COMMENT '业务功能执行耗时，单位：ms',
  `dt_create_tm` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `c_send_flag1` char(1) NOT NULL COMMENT '发往收费站 ，默认2 0-无需上传 1-未上传  2-已上传',
  `c_send_flag2` char(1) NOT NULL COMMENT '发往省中心 默认为1 0-无需上传 1-未上传  2-已上传',
  PRIMARY KEY (`vc_id`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='etc续期记录表';