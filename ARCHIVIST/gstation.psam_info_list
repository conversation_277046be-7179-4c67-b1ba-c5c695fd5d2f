CREATE TABLE IF NOT EXISTS `psam_info_list` (
  `vc_gantry_id` varchar(19) COLLATE utf8mb4_bin NOT NULL COMMENT '门架编号',
  `n_sect_gantry_id` int(5) NOT NULL COMMENT '省内门架编号',
  `vc_lane_num` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车道编号',
  `ts_create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据生成时间',
  `vc_channel_id` varchar(10) COLLATE utf8mb4_bin NOT NULL COMMENT '通道ID',
  `vc_status` varchar(10) COLLATE utf8mb4_bin NOT NULL COMMENT '工作状态',
  KEY `idx_gantry_id` (`vc_gantry_id`),
  KEY `idx_sect_gantry_id` (`n_sect_gantry_id`),
  KEY `idx_lane_num` (`vc_lane_num`),
  KEY `idx_create_time` (`ts_create_time`),
  KEY `idx_channel_id` (`vc_channel_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


