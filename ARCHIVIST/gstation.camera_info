CREATE TABLE IF NOT EXISTS `camera_info` (
  `vc_point_num` varchar(20) NOT NULL COMMENT '识别点编号',
  `n_camera_num` decimal(3,0) NOT NULL COMMENT '识别器编号',
  `n_station_id` decimal(4,0) NOT NULL COMMENT '收费站编码',
  `n_lane_id` decimal(6,0) DEFAULT NULL COMMENT '车道编号',
  `vc_pile_num` varchar(50) DEFAULT NULL COMMENT '桩号',
  `vc_brand` varchar(50) NOT NULL COMMENT '厂家型号',
  `vc_ip_address` varchar(15) NOT NULL COMMENT 'IP地址',
  `n_port` decimal(6,0) NOT NULL COMMENT '端口号',
  `n_pixel` decimal(10,0) NOT NULL COMMENT '识别图片像素 单位：万',
  `vc_resolution` varchar(50) NOT NULL COMMENT '图像分辨率',
  `c_shoot_position` char(1) NOT NULL COMMENT '拍摄位置 1-车头，2-车尾',
  `c_status` char(1) NOT NULL COMMENT '使用状态 0-停用，1-启用',
  `vc_temp` varchar(256) DEFAULT NULL COMMENT '备用字段',
  PRIMARY KEY (`vc_point_num`,`n_camera_num`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


