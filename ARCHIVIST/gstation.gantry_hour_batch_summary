CREATE TABLE IF NOT EXISTS `gantry_hour_batch_summary` (
  `vc_collect_id` varchar(32) COLLATE utf8mb4_bin NOT NULL,
  `vc_gantry_id` varchar(19) COLLATE utf8mb4_bin NOT NULL COMMENT '门架编号',
  `n_sect_gantry_id` int(5) DEFAULT NULL COMMENT '省内门架编号',
  `n_computer_order` int(1) DEFAULT NULL COMMENT '控制器序号',
  `n_collect_date` int(8) DEFAULT NULL COMMENT '统计日期',
  `vc_collect_hour_batch` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '统计小时批次',
  `n_batch_count` int(8) DEFAULT NULL COMMENT '交易流水批次内总数',
  `vc_etc_type_count` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'etc车型交易量',
  `vc_etc_class_count` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'etc车种交易量',
  `n_etc_success_count` int(8) DEFAULT NULL COMMENT 'etc成功交易量',
  `d_etc_success_fee` decimal(10,2) DEFAULT NULL COMMENT 'etc成功交易额',
  `n_etc_fail_count` int(8) DEFAULT NULL COMMENT 'etc失败交易量',
  `vc_cpc_type_count` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'cpc车型交易量',
  `vc_cpc_class_count` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'cpc车种交易量',
  `vc_cpc_success_count` int(8) DEFAULT NULL COMMENT 'cpc成功交易量',
  `d_cpc_success_fee` decimal(10,2) DEFAULT NULL COMMENT 'cpc成功交易额',
  `n_cpc_fail_count` int(8) DEFAULT NULL COMMENT 'cpc失败交易量',
  `vc_msg_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '接收的通知消息号',
  `n_station_batch_count` int(8) DEFAULT NULL COMMENT '收费站交易流水批次内总数',
  `n_station_pay_fee_count` bigint(10) DEFAULT NULL COMMENT '收费站交易额，单位：分',
  `n_diff_batch_count` int(8) DEFAULT '-1' COMMENT '统计数差值，收费站与门架统计差值(第一次)',
  `dt_update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `n_check_state` int(8) DEFAULT NULL COMMENT '核对状态，0-未平；1-平；',
  `n_upload_flag` int(8) DEFAULT NULL COMMENT '上传状态，-1-未核对,0-未传输；1-已传输',
  `dt_upload_time` datetime DEFAULT NULL COMMENT '上传省时间',
  PRIMARY KEY (`vc_collect_id`),
  KEY `idx_gantry_id` (`vc_gantry_id`),
  KEY `idx_sect_gantry_id` (`n_sect_gantry_id`),
  KEY `idx_collect_date` (`n_collect_date`),
  KEY `idx_collect_hour_batch` (`vc_collect_hour_batch`),
  KEY `idx_n_check_state` (`n_check_state`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


