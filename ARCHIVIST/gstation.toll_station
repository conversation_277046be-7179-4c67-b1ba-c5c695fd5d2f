CREATE TABLE IF NOT EXISTS `toll_station` (
  `n_station_id` int(4) NOT NULL COMMENT '管理收费站编码',
  `vc_station_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收费站名称',
  `n_road_id` int(4) NOT NULL COMMENT '所属收费公路编码',
  `vc_road_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收费路段名称',
  `n_company_id` int(7) NOT NULL COMMENT '所属路段公司编码',
  `vc_company_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '所属路段公司名称',
  `n_owner_id` int(6) NOT NULL COMMENT '业主代码',
  `vc_owner_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '业主名称',
  `n_last_init_date` int(8) DEFAULT NULL COMMENT '更新时间',
  `n_init_date` int(8) DEFAULT NULL COMMENT '初始化日期',
  `c_init` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否初始化',
  `n_open_date` int(8) DEFAULT NULL COMMENT '开通日期',
  `n_lane_cnt` int(3) DEFAULT NULL COMMENT '车道类别',
  `c_switch_code` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '工班切换代码',
  PRIMARY KEY (`n_station_id`),
  KEY `idx_road_id` (`n_road_id`),
  KEY `idx_company_id` (`n_company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


