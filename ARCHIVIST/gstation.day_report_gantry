CREATE TABLE IF NOT EXISTS `day_report_gantry` (
  `vc_gantry_id` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '门架编号',
  `n_date` int(8) NOT NULL COMMENT '工班日期',
  `n_all_count` int(8) DEFAULT NULL COMMENT '通行总量',
  `d_all_fee` decimal(10,2) DEFAULT NULL COMMENT '通行总金额',
  `n_etc_successcount` int(8) DEFAULT NULL COMMENT 'ETC成功交易量',
  `d_etc_successfee` decimal(10,2) DEFAULT NULL COMMENT 'ETC成功交易额',
  `n_etc_failcount` int(8) DEFAULT NULL COMMENT 'ETC失败交易量',
  `d_etc_failfee` decimal(10,2) DEFAULT NULL COMMENT 'ETC失败交易额',
  `n_cpc_successcount` int(8) DEFAULT NULL COMMENT 'CPC成功交易量',
  `d_cpc_successfee` decimal(10,2) DEFAULT NULL COMMENT 'CPC成功交易额',
  `n_cpc_failcount` int(8) DEFAULT NULL COMMENT 'CPC失败交易量',
  `d_cpc_failfee` decimal(10,2) DEFAULT NULL COMMENT 'CPC失败交易额',
  `c_mot_send_flag` tinyint(4) DEFAULT '0' COMMENT '部中心发送标志',
  `c_center_send_flag` tinyint(4) DEFAULT '0' COMMENT '省中心发送标志',
  `c_midcenter_send_flag` tinyint(4) DEFAULT '0' COMMENT '路段中心发送标志',
  `c_subcenter_send_flag` tinyint(4) DEFAULT '0' COMMENT '分中心发送标志',
  `c_station_send_flag` tinyint(4) DEFAULT '0' COMMENT '收费站发送标志',
  KEY `idx_gantry_id` (`vc_gantry_id`),
  KEY `idx_date` (`n_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


