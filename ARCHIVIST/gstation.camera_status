CREATE TABLE IF NOT EXISTS `camera_status` (
  `vc_point_num` varchar(20) NOT NULL COMMENT '识别点编号',
  `n_camera_num` decimal(3,0) NOT NULL COMMENT '识别器编号',
  `n_date` decimal(8,0) NOT NULL COMMENT '日期',
  `n_time` decimal(6,0) NOT NULL COMMENT '时间',
  `n_lane_id` decimal(6,0) DEFAULT NULL COMMENT '车道编号 收费站必填，其他非必填',
  `c_connect_status` char(1) NOT NULL DEFAULT '0' COMMENT '连接状态 0-断开，1-连接',
  `c_work_status` char(1) NOT NULL DEFAULT '0' COMMENT '工作状态 0-关闭，1-工作',
  `d_recognition_rate` decimal(5,2) NOT NULL COMMENT '识别率 %，带两位小数',
  `vc_hardware_version` varchar(20) NOT NULL COMMENT '固件版本',
  `n_running_time` decimal(20,0) NOT NULL COMMENT '设备运行时间 从开机到现在的运行时间（秒）',
  PRIMARY KEY (`vc_point_num`,`n_camera_num`,`n_date`,`n_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


