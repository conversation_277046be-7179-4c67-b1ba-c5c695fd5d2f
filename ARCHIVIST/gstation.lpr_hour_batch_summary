CREATE TABLE IF NOT EXISTS `lpr_hour_batch_summary` (
  `vc_collect_id` varchar(29) COLLATE utf8mb4_bin NOT NULL COMMENT '门架牌识汇总编号',
  `vc_gantry_id` varchar(19) COLLATE utf8mb4_bin NOT NULL COMMENT '门架编号',
  `n_sect_gantry_id` int(5) DEFAULT NULL COMMENT '省内门架编号',
  `n_computer_order` int(1) DEFAULT NULL COMMENT '控制器序号',
  `n_collect_date` int(8) DEFAULT NULL COMMENT '统计日期',
  `vc_collect_hour_batch` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '统计小时批次',
  `n_vehicle_data_count` int(8) DEFAULT NULL COMMENT '牌识流水数',
  `n_vehicle_pic_count` int(8) DEFAULT NULL COMMENT '牌识图片流水数',
  `vc_msg_id` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '通知消息号',
  PRIMARY KEY (`vc_collect_id`),
  KEY `idx_gantry_id` (`vc_gantry_id`),
  KEY `idx_sect_gantry_id` (`n_sect_gantry_id`),
  KEY `idx_collect_date` (`n_collect_date`),
  KEY `idx_collect_hour_batch` (`vc_collect_hour_batch`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


