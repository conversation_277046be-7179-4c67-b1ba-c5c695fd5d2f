CREATE TABLE IF NOT EXISTS `lane_http_info` (
  `n_lane_id` int(7) NOT NULL DEFAULT '0' COMMENT '车道号',
  `vc_ip` varchar(20) COLLATE utf8_croatian_ci NOT NULL DEFAULT '0' COMMENT 'IP',
  `n_http_port` int(5) NOT NULL DEFAULT '0' COMMENT '端口号',
  `n_status` int(4) NOT NULL DEFAULT '0' COMMENT '0-正常；1-异常',
  `dt_update_time` datetime DEFAULT NULL COMMENT '更新',
  `n_lane_soft_type` int(4) NOT NULL DEFAULT '0' COMMENT '0-老车道；1-芸车道'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_croatian_ci ROW_FORMAT=DYNAMIC;


