CREATE TABLE IF NOT EXISTS `lane_db_arg` (
  `lane_id` int(6) NOT NULL COMMENT '车道编号',
  `db_ip` varchar(255) DEFAULT NULL COMMENT '车道数据库ip',
  `db_name` varchar(255) DEFAULT NULL COMMENT '车道数据库名称',
  `db_port` int(4) DEFAULT NULL COMMENT '车道数据库端口',
  `db_username` varchar(255) DEFAULT NULL COMMENT '车道数据库用户名',
  `db_password` varchar(255) DEFAULT NULL COMMENT '车道数据库密码',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`lane_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='车道数据库参数表';


