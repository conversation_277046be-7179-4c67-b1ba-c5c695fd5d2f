CREATE TABLE IF NOT EXISTS `gantry_image_jour` (
  `vc_pic_id` varchar(38) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '牌识流水号',
  `vc_gantry_id` varchar(19) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '门架编号',
  `n_sect_gantry_id` int(5) NOT NULL COMMENT '省内门架编号',
  `n_gantry_order_num` int(3) NOT NULL COMMENT '门架顺序号',
  `n_drive_dir` tinyint(4) NOT NULL COMMENT '行驶方向',
  `vc_gantry_hex` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '门架Hex值',
  `ts_timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '抓拍时间',
  `n_date` int(8) NOT NULL COMMENT '拍摄日期',
  `n_time` int(6) NOT NULL COMMENT '拍摄时间',
  `n_camera_num` int(3) NOT NULL COMMENT '牌识编号',
  `vc_hour_batch_no` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '小时批次号',
  `c_shoot_position` tinyint(4) NOT NULL COMMENT '拍摄位置',
  `n_lane_num` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '物理车道编码',
  `n_station_id` int(4) NOT NULL COMMENT '收费站编码',
  `vc_iden_license` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '识别车牌号码',
  `n_vehicle_speed` int(4) DEFAULT NULL COMMENT '行驶速度',
  `n_identify_type` tinyint(4) DEFAULT NULL COMMENT '识别车型',
  `vc_vehicle_model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车辆品牌型号',
  `vc_vehicle_color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车身颜色',
  `vc_license` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '车牌号码',
  `n_image_size` int(8) NOT NULL COMMENT '车辆全景图片大小',
  `vc_image_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车辆全景图片相对路径',
  `n_license_image_size` int(8) DEFAULT NULL COMMENT '车牌图片大小',
  `vc_license_image_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车牌图片相对路径',
  `n_bin_image_size` int(8) DEFAULT NULL COMMENT '二值图片大小',
  `vc_bin_image_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '二值化图相对路径',
  `d_reliability` decimal(5,2) NOT NULL COMMENT '车牌可信度',
  `vc_vehicle_feature_code` varchar(2050) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车脸识别特征码和算法版本号',
  `vc_face_feature_code` varchar(1050) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '人脸识别特征码和算法版本号',
  `vc_verify_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '校验码',
  `vc_trade_id` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '计费交易编号',
  `vc_pass_id` varchar(40) DEFAULT NULL COMMENT '通行标识ID',
  `n_match_status` tinyint(4) DEFAULT '0' COMMENT '匹配状态',
  `n_duplicate_flag` tinyint(4) DEFAULT '0' COMMENT '去重状态',
  `n_deal_status` tinyint(4) DEFAULT '0' COMMENT '处理状态',
  `vc_relate_pic_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '关联图片流水号',
  `vc_all_relate_pic_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '全部关联图片流水号',
  `ts_station_db_time` datetime NOT NULL DEFAULT '2019-01-01 01:00:00' COMMENT '门架后台入库时间',
  `ts_station_deal_time` datetime NOT NULL DEFAULT '2019-01-01 01:00:00' COMMENT '门架后台处理时间',
  `ts_station_valid_time` datetime NOT NULL DEFAULT '2019-01-01 01:00:00' COMMENT '门架后台去重时间',
  `ts_station_match_time` datetime NOT NULL DEFAULT '2019-01-01 01:00:00' COMMENT '门架后台匹配时间',
  `c_mot_send_flag` tinyint(4) DEFAULT '0' COMMENT '部中心发送标志',
  `c_center_send_flag` tinyint(4) DEFAULT '0' COMMENT '省中心发送标志',
  `c_midcenter_send_flag` tinyint(4) DEFAULT '0' COMMENT '路段中心发送标志',
  `c_subcenter_send_flag` tinyint(4) DEFAULT '0' COMMENT '分中心发送标志',
  `c_station_send_flag` tinyint(4) DEFAULT '0' COMMENT '收费站发送标志',
  PRIMARY KEY (`vc_pic_id`),
  KEY `idx_vc_iden_license` (`vc_iden_license`),
  KEY `idx_vc_license` (`vc_license`),
  KEY `idx_vc_gantry_id` (`vc_gantry_id`),
  KEY `idx_vc_gantry_hex` (`vc_gantry_hex`),
  KEY `idx_n_station_id` (`n_station_id`),
  KEY `idx_n_date` (`n_date`),
  KEY `idx_n_time` (`n_time`),
  KEY `idx_ts_timestamp` (`ts_timestamp`),
  KEY `idx_n_camera_num` (`n_camera_num`),
  KEY `idx_vc_trade_id` (`vc_trade_id`),
  KEY `idx_vc_pass_id` (`vc_pass_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


