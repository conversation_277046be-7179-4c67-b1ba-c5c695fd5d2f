CREATE TABLE IF NOT EXISTS `rsu_base_info` (
  `n_gantry_id` int(5) NOT NULL COMMENT '省内门架编号',
  `n_gantry_num` int(11) NOT NULL COMMENT '门架排列序号',
  `vc_rsu_manu_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '门架rsu厂商代码',
  `vc_rsu_model` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '门架rsu型号',
  `vc_rsu_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '门架rsu编号',
  `vc_rsu_ver` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '门架rsu软件版本号',
  `vc_reserved` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段',
  `n_operation_type` int(11) DEFAULT '0' COMMENT '操作',
  `n_op_date` int(11) DEFAULT '0' COMMENT '操作日期',
  `n_op_time` int(11) DEFAULT '0' COMMENT '操作时间',
  KEY `idx_n_gantry_id` (`n_gantry_id`),
  KEY `idx_vc_rsu_id` (`vc_rsu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


