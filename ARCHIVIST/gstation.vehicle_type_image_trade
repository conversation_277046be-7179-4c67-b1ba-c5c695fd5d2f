CREATE TABLE IF NOT EXISTS `vehicle_type_image_trade` (
  `picture_id` varchar(50) NOT NULL COMMENT '图片唯一码:车道HEX(10位)+yyyMMddHHmmss(14位)+2位序号',
  `shift_date` int(10) DEFAULT NULL COMMENT '工班日期，格式：yyyyMMdd',
  `lane_id` int(6) DEFAULT NULL COMMENT '出口车道编号',
  `serial_no` int(5) DEFAULT NULL COMMENT '流水号',
  `trade_id` varchar(50) DEFAULT NULL COMMENT '流水ID',
  `vehicle_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pic_time` datetime DEFAULT NULL COMMENT '拍摄时间',
  `vehicle_type` tinyint(3) DEFAULT NULL COMMENT '车型',
  `axis_num` tinyint(3) DEFAULT NULL COMMENT '车轴',
  `axis_type` varchar(10) DEFAULT NULL COMMENT '轴型组合',
  `wheel_base` varchar(20) DEFAULT NULL COMMENT '轴距，单位：米',
  `vehicle_features` tinyint(3) DEFAULT NULL COMMENT '车辆特征：0-普通车；1-集装箱车；2-危化 品车；3-货车列车或半挂汽车列车',
  `length` varchar(20) DEFAULT NULL COMMENT '车长，单位：米',
  `speed` varchar(20) DEFAULT NULL COMMENT '车速，单位：km/h',
  `device_no` varchar(30) DEFAULT NULL COMMENT '设备编号',
  `manu_id` varchar(30) DEFAULT NULL COMMENT '厂商代码',
  `side_image_path` varchar(200) DEFAULT NULL COMMENT '车身图片路径',
  `side_image_size` int(10) DEFAULT NULL COMMENT '车身图片大小，单位：B，JPEG格式',
  `front_image_path` varchar(200) DEFAULT NULL COMMENT '车头图片路径 ',
  `front_image_size` int(10) DEFAULT NULL COMMENT '车头图片大小，单位：B，JPEG格式',
  `tail_image_path` varchar(200) DEFAULT NULL COMMENT '车尾图片路径',
  `tail_image_size` int(10) DEFAULT NULL COMMENT '车尾图片大小，单位：B，JPEG格式',
  `picture_status` tinyint(3) DEFAULT NULL COMMENT '图片状态，0-未上传；1-已上传',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modified_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `fix_flag` tinyint(3) DEFAULT NULL COMMENT '修复标志，0-未修复；1-已修复',
  `upload_flag` tinyint(3) DEFAULT NULL COMMENT '上传省中心标志',
  `upload_time` timestamp NULL DEFAULT NULL COMMENT '上传省中心时间',
  `upload_station_flag` tinyint(3) DEFAULT NULL COMMENT '上传站标志，0-未上传；1-已上传',
  `upload_station_time` timestamp NULL DEFAULT NULL COMMENT '上传站时间',
  `upload_road_flag` tinyint(3) DEFAULT NULL COMMENT '上传路标志，0-未上传；1-已上传',
  `upload_road_time` datetime DEFAULT NULL COMMENT '上传路时间',
  `upload_ministry_flag` tinyint(3) DEFAULT NULL COMMENT '上传部标志，0-未上传；1-已上传',
  `upload_ministry_time` datetime DEFAULT NULL COMMENT '上传部时间',
  `upload_config_flag` tinyint(3) DEFAULT NULL COMMENT '上传配置中心标志，0-未上传；1-已上传',
  `upload_config_time` datetime DEFAULT NULL COMMENT '上传配置中心时间',
  PRIMARY KEY (`picture_id`),
  KEY `vehicle_type_image_trade__index2` (`vehicle_id`),
  KEY `vehicle_type_image_trade_index_1` (`shift_date`),
  KEY `vehicle_type_image_trade_index_3` (`lane_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='车型图片流水表';


