CREATE TABLE IF NOT EXISTS `tax_code_mapping` (
  `vc_tax_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '部编码',
  `vc_code_type` varchar(5) COLLATE utf8mb4_bin NOT NULL COMMENT '编码类型',
  `vc_target_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '浙江编码',
  `c_transfer_state` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '传输状态',
  `c_data_state` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '数据状态',
  `n_task_id` int(20) DEFAULT NULL COMMENT '处理任务号',
  `n_transfer_date` int(8) DEFAULT NULL COMMENT '处理日期',
  `n_transfer_time` int(6) DEFAULT NULL COMMENT '处理时间',
  PRIMARY KEY (`vc_tax_id`,`vc_code_type`),
  KEY `idx_target_id` (`vc_target_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


