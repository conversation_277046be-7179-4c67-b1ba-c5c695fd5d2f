CREATE TABLE IF NOT EXISTS `day_report_exit_jour` (
  `vc_id` varchar(60) COLLATE utf8mb4_bin NOT NULL COMMENT '合计数编号',
  `vc_date` varchar(10) COLLATE utf8mb4_bin NOT NULL COMMENT '统计日期',
  `vc_etc_typecount` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'ETC车型处理量',
  `vc_etc_classcount` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'ETC车种处理量',
  `n_etc_successcount` int(8) DEFAULT NULL COMMENT 'ETC成功处理量',
  `n_etc_failcount` int(8) DEFAULT NULL COMMENT 'ETC失败处理量',
  `vc_cpc_typecount` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'CPC车型处理量',
  `vc_cpc_classcount` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'CPC车种处理量',
  `n_cpc_successcount` int(8) DEFAULT NULL COMMENT 'CPC成功处理量',
  `d_cpc_successfee` decimal(10,2) DEFAULT NULL COMMENT 'CPC成功交易额',
  `n_cpc_failcount` int(8) DEFAULT NULL COMMENT 'CPC失败交易量',
  `d_cpc_failfee` decimal(10,2) DEFAULT NULL COMMENT 'CPC失败交易额',
  `n_paper_successcount` int(8) DEFAULT NULL COMMENT '纸券成功交易量',
  `d_paper_successfee` decimal(10,2) DEFAULT NULL COMMENT '纸券成功交易额',
  `n_paper_failcount` int(8) DEFAULT NULL COMMENT '纸券失败交易量',
  `d_paper_failfee` decimal(10,2) DEFAULT NULL COMMENT '纸券失败交易额',
  `c_mot_send_flag` tinyint(4) DEFAULT '0' COMMENT '部中心发送标志',
  `ts_mot_send_time` datetime DEFAULT NULL COMMENT '发送部站接口时间',
  PRIMARY KEY (`vc_id`) USING BTREE,
  KEY `idx_date` (`vc_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;


