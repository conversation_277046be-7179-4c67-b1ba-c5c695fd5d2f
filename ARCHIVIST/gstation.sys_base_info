CREATE TABLE IF NOT EXISTS `sys_base_info` (
  `n_lane_num` int(7) NOT NULL COMMENT '车道编号',
  `vc_server_address` varchar(50) DEFAULT NULL COMMENT '称重服务地址',
  `vc_time_server` varchar(15) NOT NULL COMMENT '时间服务器地址',
  `vc_system_vers` varchar(50) NOT NULL COMMENT '系统版本',
  `n_status` tinyint(4) DEFAULT '0' COMMENT '是否已同步',
  PRIMARY KEY (`n_lane_num`) USING BTREE,
  UNIQUE KEY `IDX_LANE_NUM_IDX` (`n_lane_num`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='基础数据';


