CREATE TABLE IF NOT EXISTS `lpr_auth_info` (
  `n_gantry_id` int(5) NOT NULL COMMENT '省内门架编号',
  `n_gantry_num` int(11) NOT NULL COMMENT '门架排列序号',
  `n_point_type` int(1) NOT NULL COMMENT '识别点类型',
  `c_direction` char(1) COLLATE utf8mb4_bin NOT NULL COMMENT '行驶方向',
  `vc_lpr_server` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '识别器服务地址',
  `vc_time_server` varchar(15) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '时间服务器',
  `vc_sign_key` varchar(256) COLLATE utf8mb4_bin NOT NULL COMMENT '签名密钥',
  `n_first` int(1) NOT NULL COMMENT '首次认证',
  `n_expired_time` datetime NOT NULL COMMENT '失效时间',
  `vc_reserved` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段',
  KEY `idx_n_gantry_id` (`n_gantry_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


