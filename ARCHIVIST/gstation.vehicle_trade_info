CREATE TABLE IF NOT EXISTS `vehicle_trade_info` (
  `vc_trade_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `vc_pass_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `n_lane_id` int(11) NOT NULL,
  `n_date` int(11) NOT NULL,
  `n_time` int(11) NOT NULL,
  `n_sys_date` int(11) NOT NULL,
  `c_lane_type` char(1) COLLATE utf8mb4_unicode_ci NOT NULL,
  `vc_license` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `c_ticket_type` char(1) COLLATE utf8mb4_unicode_ci NOT NULL,
  `c_category` char(1) COLLATE utf8mb4_unicode_ci NOT NULL,
  `c_pass_type` char(1) COLLATE utf8mb4_unicode_ci NOT NULL,
  `dt_tm1` timestamp(3) NULL DEFAULT NULL,
  `dt_tm2` timestamp(3) NULL DEFAULT NULL,
  `dt_tm3` timestamp(3) NULL DEFAULT NULL,
  `dt_tm4` timestamp(3) NULL DEFAULT NULL,
  `dt_tm5` timestamp(3) NULL DEFAULT NULL,
  `dt_tm6` timestamp(3) NULL DEFAULT NULL,
  `dt_tm7` timestamp(3) NULL DEFAULT NULL,
  `dt_tm8` timestamp(3) NULL DEFAULT NULL,
  `dt_tm9` timestamp(3) NULL DEFAULT NULL,
  `dt_tm10` timestamp(3) NULL DEFAULT NULL,
  `dt_tm11` timestamp(3) NULL DEFAULT NULL,
  `dt_tm12` timestamp(3) NULL DEFAULT NULL,
  `dt_tm13` timestamp(3) NULL DEFAULT NULL,
  `dt_tm14` timestamp(3) NULL DEFAULT NULL,
  `dt_tm15` timestamp(3) NULL DEFAULT NULL,
  `dt_tm16` timestamp(3) NULL DEFAULT NULL,
  `dt_tm17` timestamp(3) NULL DEFAULT NULL,
  `dt_tm18` timestamp(3) NULL DEFAULT NULL,
  `dt_tm19` timestamp(3) NULL DEFAULT NULL,
  `dt_tm20` timestamp(3) NULL DEFAULT NULL,
  `dt_tm21` timestamp(3) NULL DEFAULT NULL,
  `dt_tm22` timestamp(3) NULL DEFAULT NULL,
  `dt_tm23` timestamp(3) NULL DEFAULT NULL,
  `dt_tm24` timestamp(3) NULL DEFAULT NULL,
  `dt_tm25` timestamp(3) NULL DEFAULT NULL,
  `c_send_flag1` char(1) COLLATE utf8mb4_unicode_ci NOT NULL,
  `c_send_flag2` char(1) COLLATE utf8mb4_unicode_ci NOT NULL,
  `c_send_flag3` char(1) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`vc_trade_id`,`n_lane_id`),
  KEY `vehicle_trade_info_idx1` (`c_pass_type`,`n_lane_id`,`vc_license`) USING BTREE,
  KEY `vehicle_trade_info_idx2` (`n_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


