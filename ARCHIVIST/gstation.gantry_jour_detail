CREATE TABLE IF NOT EXISTS `gantry_jour_detail` (
  `vc_gantry_id` varchar(20) NOT NULL,
  `vc_gantry_group_id` varchar(20) DEFAULT NULL,
  `c_direction` char(1) DEFAULT NULL,
  `n_gantry_num` int(11) DEFAULT NULL,
  `n_date` decimal(8,0) NOT NULL,
  `n_time` decimal(6,0) NOT NULL,
  `n_serial` decimal(8,0) NOT NULL,
  `vc_pass_id` varchar(32) DEFAULT NULL,
  `n_tran_date` decimal(8,0) DEFAULT NULL,
  `n_tran_time` decimal(6,0) DEFAULT NULL,
  `vc_license` varchar(20) DEFAULT NULL,
  `c_category` char(1) DEFAULT NULL,
  `vc_vehicle_class` varchar(2) DEFAULT NULL,
  `n_vehicle_usertype` decimal(8,0) DEFAULT NULL,
  `vc_obu_status` varchar(4) DEFAULT NULL,
  `vc_obu_provider1` varchar(4) DEFAULT NULL,
  `vc_obu_provider2` varchar(8) DEFAULT NULL,
  `c_obu_contracttype` char(1) DEFAULT NULL,
  `vc_obu_contractver` varchar(2) DEFAULT NULL,
  `vc_obu_contractno` varchar(16) DEFAULT NULL,
  `vc_obu_macid` varchar(8) DEFAULT NULL,
  `n_obu_issuedate` decimal(8,0) DEFAULT NULL,
  `n_obu_expiredate` decimal(8,0) DEFAULT NULL,
  `vc_obu_marks` varchar(512) DEFAULT NULL,
  `vc_cpu_status` varchar(4) DEFAULT NULL,
  `vc_cpu_provider1` varchar(4) DEFAULT NULL,
  `vc_cpu_provider2` varchar(8) DEFAULT NULL,
  `n_cpu_cardtype` decimal(8,0) DEFAULT NULL,
  `vc_cpu_cardver` varchar(2) DEFAULT NULL,
  `vc_cpu_netid` varchar(4) DEFAULT NULL,
  `vc_cpu_cardid` varchar(22) DEFAULT NULL,
  `n_cpu_issuedate` decimal(8,0) DEFAULT NULL,
  `n_cpu_expiredate` decimal(8,0) DEFAULT NULL,
  `vc_cpu_marks` varchar(512) DEFAULT NULL,
  `d_acount_before` decimal(12,2) DEFAULT NULL,
  `d_consume_toll` decimal(12,2) DEFAULT NULL,
  `d_svc_balance` decimal(12,2) DEFAULT NULL,
  `c_lane_type` char(1) DEFAULT NULL,
  `c_tran_category` char(1) DEFAULT NULL,
  `vc_terminal_number` varchar(12) DEFAULT NULL,
  `vc_terminal_tranno` varchar(8) DEFAULT NULL,
  `vc_tranno` varchar(4) DEFAULT NULL,
  `vc_tac` varchar(8) DEFAULT NULL,
  `n_tran_status` decimal(8,0) DEFAULT NULL,
  `vc_tran_event` varchar(4) DEFAULT NULL,
  `vc_fee_event_desc` varchar(256) DEFAULT NULL,
  `n_rsumanuid` decimal(6,0) DEFAULT NULL,
  `vc_rsuid` varchar(6) DEFAULT NULL,
  `c_service_type` char(1) DEFAULT NULL,
  `c_interrupt_singal` char(1) DEFAULT NULL,
  `n_start_date` decimal(8,0) DEFAULT NULL,
  `n_start_time` decimal(6,0) DEFAULT NULL,
  `n_begin_time` decimal(6,0) DEFAULT NULL,
  `n_begin_time_ms` decimal(6,0) DEFAULT NULL,
  `n_duration` decimal(8,0) DEFAULT NULL,
  `d_time_total` decimal(9,3) DEFAULT NULL,
  `d_time_obu_r` decimal(9,3) DEFAULT NULL,
  `d_time_cpu_r` decimal(9,3) DEFAULT NULL,
  `d_time_obu_w` decimal(9,3) DEFAULT NULL,
  `d_time_cpu_w` decimal(9,3) DEFAULT NULL,
  `d_time_check` decimal(9,3) DEFAULT NULL,
  `d_time_fare` decimal(9,3) DEFAULT NULL,
  `d_time_consume` decimal(9,3) DEFAULT NULL,
  `d_time_insert` decimal(9,3) DEFAULT NULL,
  `d_time_obu_readmark` decimal(9,3) DEFAULT NULL,
  `d_time_cpu_readmark` decimal(9,3) DEFAULT NULL,
  `d_time_obu_writemark` decimal(9,3) DEFAULT NULL,
  `d_time_cpu_writemark` decimal(9,3) DEFAULT NULL,
  `c_status_obu_writemark` char(1) DEFAULT NULL,
  `c_status_cpu_writemark` char(1) DEFAULT NULL,
  `c_status_obu_readmark` char(1) DEFAULT NULL,
  `c_status_cpu_readmark` char(1) DEFAULT NULL,
  `n_temp_1` decimal(8,0) DEFAULT NULL,
  `n_temp_2` decimal(8,0) DEFAULT NULL,
  `d_temp_3` decimal(9,3) DEFAULT NULL,
  `d_temp_4` decimal(9,3) DEFAULT NULL,
  `vc_temp_5` varchar(10) DEFAULT NULL,
  `vc_temp_6` varchar(10) DEFAULT NULL,
  `c_mot_send_flag` char(1) DEFAULT NULL,
  `c_center_send_flag` char(1) DEFAULT NULL,
  `c_midcenter_send_flag` char(1) DEFAULT NULL,
  `c_subcenter_send_flag` char(1) DEFAULT NULL,
  `c_station_send_flag` char(1) DEFAULT NULL,
  PRIMARY KEY (`vc_gantry_id`,`n_date`,`n_time`,`n_serial`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


