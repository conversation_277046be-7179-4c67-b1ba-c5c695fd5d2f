CREATE TABLE IF NOT EXISTS `gantry_image_store` (
  `vc_pic_id` varchar(36) DEFAULT NULL,
  `vc_gantry_id` varchar(20) DEFAULT NULL,
  `n_date` decimal(8,0) DEFAULT NULL,
  `n_time` decimal(6,0) DEFAULT NULL,
  `vc_sequence_no` varchar(2) DEFAULT NULL,
  `c_store_type` char(1) DEFAULT NULL,
  `vc_image_id` varchar(256) DEFAULT NULL,
  `vc_image_name` varchar(256) DEFAULT NULL,
  `vc_license_image_id` varchar(256) DEFAULT NULL,
  `vc_license_image_name` varchar(256) DEFAULT NULL,
  `vc_bin_image_id` varchar(256) DEFAULT NULL,
  `vc_bin_image_name` varchar(256) DEFAULT NULL,
  `c_status` char(1) DEFAULT NULL,
  `n_in_db_date` decimal(8,0) DEFAULT NULL,
  `n_in_db_time` decimal(6,0) DEFAULT NULL,
  `c_mot_send_flag` char(1) DEFAULT NULL,
  `c_midcenter_send_flag` char(1) DEFAULT NULL,
  `c_subcenter_send_flag` char(1) DEFAULT NULL,
  `c_station_send_flag` char(1) DEFAULT NULL,
  `c_center_send_flag` char(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


