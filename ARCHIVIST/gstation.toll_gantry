CREATE TABLE IF NOT EXISTS `toll_gantry` (
  `vc_gantry_id` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '门架编号',
  `vc_gantry_hex` varchar(7) COLLATE utf8mb4_bin NOT NULL COMMENT '门架hex码',
  `vc_gantry_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '门架名称',
  `n_sect_gantry_id` int(11) NOT NULL COMMENT '省内门架编号',
  `n_gantry_num` int(2) NOT NULL COMMENT '门架排列序号',
  `c_direction` char(1) COLLATE utf8mb4_bin NOT NULL COMMENT '方向',
  `n_road_id` int(4) NOT NULL COMMENT '所属收费公路编码',
  `n_company_id` int(7) NOT NULL COMMENT '所属路段公司编码',
  `n_station_id` int(4) NOT NULL COMMENT '管理收费站编码',
  `n_sect_id` int(6) NOT NULL COMMENT '所属省内断面编号',
  `vc_toll_interval_id` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '所属收费单元编号',
  `n_gantry_type` char(1) COLLATE utf8mb4_bin NOT NULL COMMENT '门架类型',
  `n_gantry_sub_type` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '门架子类型',
  `d_lat` decimal(9,6) NOT NULL COMMENT '纬度',
  `d_lng` decimal(9,6) NOT NULL COMMENT '经度',
  `vc_stake_num` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '桩号',
  `n_status` char(1) COLLATE utf8mb4_bin DEFAULT '2' COMMENT '使用状态',
  `n_start_date` int(8) NOT NULL COMMENT '起始日期',
  `n_end_date` int(8) DEFAULT '29991231' COMMENT '截止日期',
  `vc_reserved` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段',
  `n_operation_type` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '操作',
  `n_op_date` int(8) DEFAULT '0' COMMENT '操作日期',
  `n_op_time` int(8) DEFAULT '0' COMMENT '操作时间',
  PRIMARY KEY (`vc_gantry_id`),
  KEY `idx_sect_gantry_id` (`n_sect_gantry_id`),
  KEY `idx_road_id` (`n_road_id`),
  KEY `idx_company_id` (`n_company_id`),
  KEY `idx_station_id` (`n_station_id`),
  KEY `idx_toll_interval_id` (`vc_toll_interval_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


