CREATE TABLE IF NOT EXISTS `node_fare` (
  `n_standard` int(13) DEFAULT NULL COMMENT '费率版本',
  `vc_node_type` int(8) DEFAULT NULL COMMENT '节点类型',
  `vc_node_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '节点名称',
  `vc_enprovince_id` int(8) DEFAULT NULL COMMENT '入口收费站对应省域编码',
  `n_enstation_id` int(8) DEFAULT NULL COMMENT '入口收费站站码',
  `n_enstation_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '入口收费站名称',
  `n_vehicle_type` int(8) DEFAULT NULL COMMENT '车型',
  `n_length` decimal(8,4) DEFAULT NULL COMMENT '收费单元实际里程',
  `n_fee_length` decimal(8,4) DEFAULT NULL COMMENT '收费单元计费里程',
  `n_toll_intervals_count` int(8) DEFAULT NULL COMMENT '收费单元数量',
  `vc_toll_intervals_group` varchar(150) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收费单元编号组合',
  `d_fare` decimal(8,0) DEFAULT NULL COMMENT '计费金额',
  `n_fee_type` int(1) DEFAULT NULL COMMENT '费用类型',
  `n_valid_date` int(8) DEFAULT NULL COMMENT '生效日期',
  `n_valid_time` int(6) DEFAULT NULL COMMENT '生效日期',
  KEY `idx_standard` (`n_standard`),
  KEY `idx_enprovince_id` (`vc_enprovince_id`),
  KEY `idx_enstation_id` (`n_enstation_id`),
  KEY `idx_vehicle_type` (`n_vehicle_type`),
  KEY `idx_valid_datetime` (`n_valid_date`,`n_valid_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


