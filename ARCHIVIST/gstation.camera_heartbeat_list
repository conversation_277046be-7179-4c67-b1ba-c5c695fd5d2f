CREATE TABLE IF NOT EXISTS `camera_heartbeat_list` (
  `vc_gantry_id` varchar(19) COLLATE utf8mb4_bin NOT NULL COMMENT '门架编号',
  `n_sect_gantry_id` int(5) NOT NULL COMMENT '省内门架编号',
  `vc_pic_state_id` varchar(36) COLLATE utf8mb4_bin NOT NULL COMMENT '牌识状态流水号',
  `n_camera_num` int(3) NOT NULL COMMENT '牌识编号（101~299）',
  `vc_lane_num` varchar(10) COLLATE utf8mb4_bin NOT NULL COMMENT '车道编号',
  `ts_create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据生成时间',
  `n_connect_status` tinyint(4) NOT NULL COMMENT '连接状态',
  `n_work_status` tinyint(4) NOT NULL COMMENT '工作状态',
  `n_light_work_status` tinyint(4) NOT NULL COMMENT '补光灯的工作状态',
  `d_recognition_rate` decimal(5,2) DEFAULT NULL COMMENT '识别成功率',
  `vc_hardware_version` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '固件版本',
  `vc_software_version` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '软件版本',
  `n_running_time` int(11) NOT NULL COMMENT '设备从开机到现在的运行时间（秒）',
  `vc_brand` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '厂商名称',
  `vc_device_type` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '设备型号',
  `vc_status_code` varchar(10) COLLATE utf8mb4_bin NOT NULL COMMENT '状态码',
  `vc_status_msg` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '状态描述',
  KEY `idx_gantry_id` (`vc_gantry_id`),
  KEY `idx_sect_gantry_id` (`n_sect_gantry_id`),
  KEY `idx_pic_state_id` (`vc_pic_state_id`),
  KEY `idx_lane_num` (`vc_lane_num`),
  KEY `idx_create_time` (`ts_create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


