CREATE TABLE IF NOT EXISTS `stat_port_weight_detail` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `n_date` int(8) NOT NULL COMMENT '统计日期',
  `n_station_id` int(4) DEFAULT NULL COMMENT '收费站编号',
  `vc_license` varchar(16) DEFAULT NULL COMMENT '车牌',
  `vc_license_color` varchar(2) DEFAULT NULL COMMENT '车牌颜色',
  `dt_gmt_pass_station` datetime DEFAULT NULL COMMENT '通过收费站时间',
  `n_station_axle` int(4) DEFAULT NULL COMMENT '收费站车轴',
  `n_station_weight_count` int(8) DEFAULT NULL COMMENT '收费站车辆重量',
  `n_station_weight_limit` int(8) DEFAULT NULL COMMENT '收费站车辆限重',
  `dt_gmt_pass_port` datetime DEFAULT NULL COMMENT '出港时间',
  `n_port_axle` int(4) DEFAULT NULL COMMENT '港区车辆轴数',
  `n_port_weight_count` int(8) DEFAULT NULL COMMENT '港区车辆重量',
  `n_type` int(2) DEFAULT NULL COMMENT '1-港区与收费站都超限；2-港区未超限收费站超限；3-使用港区称重数据',
  `dt_gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `ts_center_send_time` datetime DEFAULT NULL,
  `c_center_send_flag` tinyint(2) DEFAULT '0' COMMENT '上传标志位',
  PRIMARY KEY (`id`),
  KEY `idx_c_center_send_flag` (`c_center_send_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


