CREATE TABLE IF NOT EXISTS `weight_device_status_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `n_lane_num` int(7) DEFAULT NULL COMMENT '站码',
  `n_device_num` int(3) NOT NULL COMMENT '设备编号（1~999）',
  `n_date` int(8) NOT NULL COMMENT '状态采集日期',
  `n_time` int(6) NOT NULL COMMENT '状态采集时间',
  `n_system_status` tinyint(4) NOT NULL COMMENT '系统运行状态 0正常 ；1异常',
  `n_device_status` int(2) NOT NULL COMMENT '设备状态：BIT0：0-正常，1-秤台传感器故障；BIT1：0-正常，1-主车辆分离器故障；BIT2：0-正常，1-辅助车辆分离器故障；BIT3：0-正常，1-轮胎识别器故障；BIT4：0-正常，1-通讯故障；BIT5：0-正常，1-缓存溢出',
  `n_status` tinyint(4) DEFAULT '0' COMMENT '同步状态, 0:未同步, 1:已同步',
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_LANE_DEVICE_IDX` (`n_lane_num`,`n_device_num`,`n_date`,`n_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备状态';


