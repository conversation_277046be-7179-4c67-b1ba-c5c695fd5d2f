CREATE TABLE IF NOT EXISTS `lpr_device_info` (
  `n_gantry_id` int(5) NOT NULL COMMENT '省内门架编号',
  `n_gantry_num` int(11) NOT NULL COMMENT '门架排列序号',
  `n_device_num` int(3) NOT NULL COMMENT '车牌识别设备编号',
  `vc_brand` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '厂家型号',
  `vc_ip_address` varchar(15) COLLATE utf8mb4_bin NOT NULL COMMENT 'IP地址',
  `n_port` int(6) NOT NULL COMMENT '端口号',
  `n_pixel` int(10) NOT NULL COMMENT '识别图片像素',
  `vc_resolution` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '图像分辨率',
  `c_shoot_position` char(1) COLLATE utf8mb4_bin NOT NULL COMMENT '拍摄位置',
  `c_direction` char(1) COLLATE utf8mb4_bin NOT NULL COMMENT '行驶方向',
  `vc_lpr_server` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '识别器服务地址',
  `vc_time_server` varchar(15) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '时间服务器',
  `c_status` char(1) COLLATE utf8mb4_bin NOT NULL COMMENT '使用状态',
  `vc_reserved` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段',
  KEY `idx_gantry_id` (`n_gantry_id`),
  KEY `idx_device_num` (`n_device_num`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


