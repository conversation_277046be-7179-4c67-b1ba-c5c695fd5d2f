CREATE TABLE IF NOT EXISTS `all_road_fare` (
  `vc_en_prov` varchar(2) NOT NULL COMMENT '入口收费站对应省域编码，2 位数字',
  `vc_en_id` varchar(32) NOT NULL COMMENT '入口收费站编码',
  `vc_ex_prov` varchar(2) NOT NULL COMMENT '出口收费站对应省域编码，2 位数字，当中出口收费站对应省域默认为请求省',
  `vc_ex_id` varchar(32) NOT NULL COMMENT '出口收费站编码',
  `vc_muti_province_sign` char(1) DEFAULT NULL COMMENT '多省路径标识 1-不存在多省路径 2-存在多省路径',
  `n_index` int(2) DEFAULT NULL COMMENT '各省途径顺序号 从1开始递增 如果1个省多次途径，则每次都独立保留',
  `vc_prov` varchar(2) DEFAULT NULL COMMENT '省份Id 2 位数字',
  `n_count` bigint(20) DEFAULT NULL COMMENT '该省收费单元数量',
  `vc_group` varchar(5000) DEFAULT NULL COMMENT '该省收费单元编号组合 使用“|”分隔。',
  `vc_m_ver` varchar(64) DEFAULT NULL COMMENT '该省计费模块版本号',
  `vc_p_ver` varchar(64) DEFAULT NULL COMMENT '该省计费参数版本号',
  `n_v_type` int(2) DEFAULT NULL COMMENT '车型',
  `vc_fee_group` varchar(5000) DEFAULT NULL COMMENT '收费单元实际收费金额组合 单位：分。使用“|”分隔。 所有单元收费金额=总实收金额',
  `n_toll_fee` bigint(20) DEFAULT NULL COMMENT '该省实收金额 单位：分',
  KEY `idx_en_prov` (`vc_en_prov`,`vc_en_id`,`vc_ex_prov`,`vc_ex_id`,`n_v_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='全网收费站间可达最短路径费率表';


