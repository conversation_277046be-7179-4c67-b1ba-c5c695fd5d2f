CREATE TABLE IF NOT EXISTS `point_info` (
  `vc_point_num` varchar(20) NOT NULL COMMENT '识别点编号',
  `c_point_type` char(1) NOT NULL COMMENT '识别点类型:0-标识点；1-省界虚拟站；2-收费站',
  `n_station_id` decimal(8,0) NOT NULL COMMENT '收费站编码',
  `c_provincial_edge` char(1) NOT NULL COMMENT '省界站标志:0-不是，1-是',
  `vc_neighbor_num` varchar(20) DEFAULT NULL COMMENT '邻省识别点编号',
  `n_direction` decimal(1,0) NOT NULL COMMENT '行驶方向:0-正向，1-反向',
  `vc_lpr_server` varchar(50) NOT NULL COMMENT '识别器服务地址',
  `vc_time_server` varchar(15) DEFAULT NULL COMMENT '时钟服务器地址',
  `vc_sign_key` varchar(256) NOT NULL COMMENT '签名密钥',
  PRIMARY KEY (`vc_point_num`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


