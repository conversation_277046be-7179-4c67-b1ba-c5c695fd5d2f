CREATE TABLE IF NOT EXISTS `image_flow` (
  `vc_point_num` varchar(20) NOT NULL COMMENT '识别点编号',
  `n_date` decimal(8,0) NOT NULL COMMENT '拍摄日期',
  `n_time` decimal(6,0) NOT NULL COMMENT '拍摄时间',
  `vc_sequence_no` varchar(3) NOT NULL COMMENT '顺序码',
  `n_camera_num` decimal(3,0) NOT NULL COMMENT '识别器编号',
  `n_station_id` decimal(4,0) NOT NULL COMMENT '收费站编码',
  `n_lane_id` decimal(6,0) DEFAULT NULL COMMENT '车道编号',
  `c_shoot_position` char(1) NOT NULL COMMENT '拍摄位置 1-车头，2-车尾',
  `vc_license_num` varchar(20) NOT NULL COMMENT '车牌号码',
  `vc_license_color` char(1) NOT NULL COMMENT '车牌颜色 0-蓝，1-黄，2-黑，3-白，4-渐变绿，5-黄绿双拼，6-蓝白渐变，9-未确定',
  `n_speed` decimal(4,0) DEFAULT NULL COMMENT '行驶速度 单位：km/h',
  `n_vehicle_class` decimal(2,0) DEFAULT NULL COMMENT '国标车型 1-一型客车，2-二型客车，3-三型客车，4-四型客车，11-一型货车，12-二型货车，13-三型货车，14-四型货车，15-五型货车',
  `vc_vehicle_model` varchar(50) DEFAULT NULL COMMENT '车辆品牌型号',
  `n_image_size` decimal(8,0) NOT NULL COMMENT '图片大小 单位：kb',
  `vc_image_path` varchar(256) DEFAULT NULL COMMENT '图片路径',
  `n_license_image_size` decimal(8,0) NOT NULL COMMENT '车牌图片大小 单位：kb',
  `vc_license_image_path` varchar(256) DEFAULT NULL COMMENT '车牌图片路径',
  `d_reliability` decimal(5,2) NOT NULL COMMENT '车牌可信度 %，带两位小数',
  `dt_in_db_time` datetime DEFAULT NULL COMMENT '保存时间',
  PRIMARY KEY (`vc_point_num`,`n_date`,`n_time`,`vc_sequence_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


