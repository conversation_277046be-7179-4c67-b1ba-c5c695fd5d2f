package agent

import (
	"context"
	"fmt"
	"github.com/gin-gonic/gin"
	. "mysql_archive/pkg/config"
	pubconn "mysql_archive/pkg/conn"
	. "mysql_archive/pkg/exr"
	"mysql_archive/pkg/zlog"
	_ "mysql_archive/src/task"
	_ "mysql_archive/src/task/row"
	. "mysql_archive/src/task/sync"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"
)

func init() {
	// 检查是否为版本查询，如果是则跳过初始化
	for _, arg := range os.Args {
		if arg == "-version" || arg == "-v" {
			return
		}
	}

	// 延迟日志初始化到Run函数中，这样可以先处理命令行参数
	Ldb1.Init()
}

func Run() {
	// 初始化日志系统（现在环境变量已经设置）
	zlog.Init("mysql_archive.log", "log")
	fmt.Printf("zlog.Init() 执行完成\n")
	zlog.Info("【调试】Run函数开始执行")
	fmt.Printf("zlog.Debug() 执行完成\n")

	// 如果配置存在就加载
	fmt.Printf("准备检查配置是否存在...\n")
	kvExists := Ldb1.KvExist("last")
	fmt.Printf("配置存在检查结果: %v\n", kvExists)
	if kvExists {
		tmpconfig, err := GetStatusInfo("last")
		if err != nil {
		}
		*Re = StatuInfo{
			StatusMap:     Re.StatusMap,
			Configversion: Re.Configversion,
			Status:        tmpconfig.Status,
			Running:       tmpconfig.Running,
			Version:       Re.Version,
			DebugLog:      tmpconfig.DebugLog,
		}

		// 根据持久化的配置设置日志级别
		fmt.Printf("准备更新日志级别，Re.DebugLog=%v\n", Re.DebugLog)
		zlog.UpdateLogLevel(Re.DebugLog)
		fmt.Printf("日志级别更新完成\n")
		if Re.DebugLog {
			zlog.Info("从持久化配置恢复Debug日志设置：已启用")
		} else {
			zlog.Info("从持久化配置恢复Debug日志设置：已禁用，只输出错误日志")
		}
	}

	fmt.Printf("if分支执行完成，准备创建调度器...\n")
	fmt.Printf("当前Re.DebugLog状态: %v\n", Re.DebugLog)

	// 强制启用DEBUG日志以便调试
	Re.DebugLog = false
	zlog.UpdateLogLevel(false)
	//fmt.Printf("强制启用DEBUG日志完成\n")
	//
	//fmt.Printf("准备输出INFO日志...\n")
	//zlog.Info("【调试】配置加载完成，准备创建调度器...")
	//fmt.Printf("INFO日志输出完成\n")
	//zlog.Info("【调试】当前Re.Running状态:", Re.Running)
	//fmt.Printf("第二个INFO日志输出完成\n")
	// 创建一个新的调度器
	scheduler := NewScheduler()
	zlog.Info("【调试】调度器创建成功")

	// 根据row date sync 启动调度
	ctx, cancel := context.WithCancel(context.Background())
	//defer cancel() // 不在这里defer，而是在信号处理中调用

	// 先启动调度器
	zlog.Info("【启动】启动调度器")
	go scheduler.ScheduleCrontab(ctx)

	// 等待调度器启动
	time.Sleep(1 * time.Second)
	zlog.Info("【启动】调度器启动完成")

	// 立即执行一次配置同步
	zlog.Info("【启动】程序启动，立即执行配置同步")
	if ok := RemoteSyncConfig(); !ok {
		zlog.Error("【启动】初始配置同步失败")
	} else {
		zlog.Info("【启动】初始配置同步成功，开始加载BackInfo")
		newBackinfo := BackInfo()
		if len(newBackinfo) > 0 {
			SetBackinfo(newBackinfo)
			zlog.Info("【启动】全局Backinfo初始化成功，表数量:", len(newBackinfo))
			for tableName := range newBackinfo {
				zlog.Debug("【启动】全局Backinfo包含表:", tableName)
			}
			// 生成调度计划和ddl配置
			scheduleTable := GenerateSchedule(newBackinfo)
			zlog.Debug("【启动】开始更新调度表")
			scheduler.UpdateScheduleTable(scheduleTable)
			zlog.Info("【启动】调度计划初始化完成")
		} else {
			zlog.Error("【启动】BackInfo为空，配置加载失败")
		}
	}

	// 获取远程配置到本地（后台持续同步）
	go func() {
		zlog.Info("【调度器】后台配置同步goroutine启动")
		syncCount := 0
		for {
			syncCount++
			zlog.Debug("【调度器】==================== 开始配置同步循环 第", syncCount, "次 ====================")
			zlog.Debug("【调度器】当前时间:", time.Now().Format("2006-01-02 15:04:05"))

			// 同步备份信息
			if ok := RemoteSyncConfig(); !ok {
				zlog.Error("【调度器】同步配置失败，第", syncCount, "次")
			} else {
				zlog.Debug("【调度器】远程配置同步成功，第", syncCount, "次，开始加载BackInfo")
				newBackinfo := BackInfo()
				if len(newBackinfo) > 0 {
					// 确保更新全局变量
					SetBackinfo(newBackinfo)
					zlog.Debug("【调度器】全局Backinfo更新成功，表数量:", len(newBackinfo))

					// 验证全局变量是否真的被更新
					for tableName := range newBackinfo {
						zlog.Debug("【调度器】全局Backinfo包含表:", tableName)
					}

					// 生成调度计划和ddl配置
					scheduleTable := GenerateSchedule(newBackinfo)
					scheduler.UpdateScheduleTable(scheduleTable)
					zlog.Info("【调度器】调度计划更新完成")
				} else {
					zlog.Warn("【调度器】BackInfo为空，跳过调度计划更新")
				}
			}
			zlog.Info("【调度器】配置同步循环完成，第", syncCount, "次，开始等待30秒")
			zlog.Info("【调度器】下次同步时间:", time.Now().Add(30*time.Second).Format("2006-01-02 15:04:05"))
			time.Sleep(30 * time.Second)
		}
	}()
	time.Sleep(5 * time.Second)

	r := gin.Default()

	// 添加统一的错误处理中间件
	r.Use(func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				zlog.Error("API请求异常:", err)
				c.JSON(http.StatusInternalServerError, map[string]interface{}{
					"success": false,
					"error":   "内部服务器错误",
					"code":    500,
				})
				c.Abort()
			}
		}()
		c.Next()
	})

	// ==================== 版本信息 API ====================
	r.GET("/api/version", func(c *gin.Context) {
		versionInfo := GetVersionInfo()
		c.JSON(http.StatusOK, map[string]interface{}{
			"success":      true,
			"version_info": versionInfo,
			"timestamp":    time.Now().Unix(),
		})
	})

	// ==================== 归档系统状态 API ====================
	// 新的RESTful API路径
	r.GET("/api/v1/archive/status", handleGetArchiveStatus)
	r.POST("/api/v1/archive/control", handlePostArchiveControl)

	// 保持向后兼容的旧API
	r.GET("/api/information/v1/mtriecs", handleGetArchiveStatus)
	r.POST("/api/information/v1/mtriecs", handlePostArchiveControl)

	// ==================== 配置信息 API ====================
	r.GET("/api/v1/archive/config", func(c *gin.Context) {
		allBackinfo := GetAllBackinfo()
		c.JSON(http.StatusOK, map[string]interface{}{
			"success": true,
			"data":    allBackinfo,
			"count":   len(allBackinfo),
		})
	})

	// 设置优雅关闭
	setupGracefulShutdown(ctx, cancel)

	r.Run(":14445")
}

// handleGetArchiveStatus 处理获取归档状态请求
func handleGetArchiveStatus(c *gin.Context) {
	zlog.Debug("【API调用】==================== API状态查询开始 ====================")
	zlog.Debug("【API调用】客户端IP:", c.ClientIP(), "请求路径:", c.Request.URL.Path)

	// 更新版本信息
	Re.VersionInfo = GetVersionInfo()

	zlog.Debug("【API调用】当前进度状态:")
	allProgress := GetAllProgress()
	for tableName, progress := range allProgress {
		if tableName == "a" || tableName == "b" || tableName == "c" {
			zlog.Debug("【API调用】  ", tableName, ":", progress, "%")

			// 触发一次进度重新计算来观察计算过程
			if progress < 100.0 {
				zlog.Debug("【API调用】触发进度重新计算，表名:", tableName)
				dbName := "gstation"
				if tableConfig, exists := GetBackinfo(tableName); exists {
					if dbNameFromConfig, hasDB := tableConfig["dbname"]; hasDB {
						dbName = dbNameFromConfig
					}
				}

				// 创建临时连接来计算进度
				db, err := pubconn.Init(*Mysqldb)
				if err == nil {
					conn := &pubconn.Conn{Db: db}
					newProgress := conn.TaskProgress(tableName, dbName)
					zlog.Debug("【API调用】重新计算的进度:", tableName, "=", newProgress, "%")
				}
			}
		}
	}

	// 构建详细的同步状态信息（使用并发安全的函数）
	syncDetails := make(map[string]interface{})
	allSyncStatus := GetAllSyncStatus()
	for tableName, syncInfo := range allSyncStatus {
		if syncInfo != nil {
			syncDetails[tableName] = map[string]interface{}{
				"status":      syncInfo.Status,
				"code":        syncInfo.Code,
				"message":     syncInfo.Message,
				"update_time": syncInfo.UpdateTime,
			}
		} else {
			syncDetails[tableName] = map[string]interface{}{
				"status":      "unknown",
				"code":        0,
				"message":     "状态未知",
				"update_time": 0,
			}
		}
	}

	// 构建进度统计
	allProgress = GetAllProgress()
	progressStats := map[string]interface{}{
		"total_tables":      len(allProgress),
		"completed_count":   0,
		"in_progress_count": 0,
		"details":           allProgress,
	}

	for _, progress := range allProgress {
		if progress >= 100.0 {
			progressStats["completed_count"] = progressStats["completed_count"].(int) + 1
		} else {
			progressStats["in_progress_count"] = progressStats["in_progress_count"].(int) + 1
		}
	}

	// 构建系统状态
	systemStatus := map[string]interface{}{
		"running":     Re.Running,
		"status_code": Re.Status,
		"debug_log":   Re.DebugLog,
		"version":     Re.VersionInfo,
		"uptime":      time.Now().Unix(),
	}

	zlog.Debug("【API调用】响应数据准备完成")
	zlog.Debug("【API调用】==================== API状态查询结束 ====================")

	c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"system":   systemStatus,
			"progress": progressStats,
			"sync":     syncDetails,
		},
		// 保持向后兼容
		"Re":       Re,
		"Progress": Progress,
		"Sync":     syncDetails,
	})
}

// handlePostArchiveControl 处理归档控制请求
func handlePostArchiveControl(c *gin.Context) {
	var requestBody struct {
		Running  *bool  `json:"running"`
		Status   *int64 `json:"status"`
		DebugLog *bool  `json:"debug_log"`
	}

	if err := c.ShouldBindJSON(&requestBody); err != nil {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "请求参数格式错误",
			"details": err.Error(),
		})
		return
	}

	// 记录操作日志
	zlog.Debug("收到归档控制请求:", requestBody)

	// 更新运行状态
	if requestBody.Running != nil {
		oldRunning := Re.Running
		Re.Running = *requestBody.Running
		if oldRunning != Re.Running {
			if Re.Running {
				zlog.Debug("归档任务已启动")
			} else {
				zlog.Debug("归档任务已停止")
			}
		}
	}

	// 更新状态码
	if requestBody.Status != nil {
		Re.Status = *requestBody.Status
	}

	// 更新Debug日志开关
	if requestBody.DebugLog != nil && *requestBody.DebugLog != Re.DebugLog {
		Re.DebugLog = *requestBody.DebugLog
		zlog.UpdateLogLevel(*requestBody.DebugLog)
		if *requestBody.DebugLog {
			zlog.Debug("Debug日志已启用")
		} else {
			zlog.Debug("Debug日志已禁用，只输出错误日志")
		}
	}

	// 持久化配置
	if err := PutStatusInfo("last", *Re); err != nil {
		zlog.Error("配置持久化失败:", err.Error())
	}

	c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "配置更新成功",
		"data": map[string]interface{}{
			"running":   Re.Running,
			"status":    Re.Status,
			"debug_log": Re.DebugLog,
		},
		// 保持向后兼容
		"Re": Re,
	})
}

// setupGracefulShutdown 设置优雅关闭
func setupGracefulShutdown(ctx context.Context, cancel context.CancelFunc) {
	// 监听系统信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		sig := <-sigChan
		zlog.Debug("收到系统信号:", sig.String(), "开始优雅关闭...")

		// 取消上下文，停止调度器
		cancel()

		// 关闭数据库连接
		if err := Ldb1.Close(); err != nil {
			zlog.Error("关闭数据库连接失败:", err.Error())
		}

		zlog.Debug("程序优雅关闭完成")
		os.Exit(0)
	}()
}
