package row

import (
	"fmt"
	. "mysql_archive/pkg/config"
	pubconn "mysql_archive/pkg/conn"
	"mysql_archive/pkg/utils"
	"mysql_archive/pkg/zlog"
	"os"
	"strings"
)

type Row struct {
	Conn *pubconn.Conn
}

func (t *Row) Run(Backinfo *JobInfo) {
	tableau := Backinfo.CronJob.JobName
	zlog.Debug("进入Row.Run方法，任务名:", tableau)

	interval := Backinfo.JobDetail.Interval
	datecloum := Backinfo.JobDetail.DateColumn
	cloumtype := Backinfo.JobDetail.ColumnType
	PRIMARY := Backinfo.JobDetail.PRIMARY
	dbname := Backinfo.JobDetail.DBName
	BatchNum := Backinfo.JobDetail.Batch

	zlog.Debug("任务参数 - 间隔:", interval, "日期列:", datecloum, "列类型:", cloumtype, "主键:", PRIMARY, "数据库:", dbname, "批次大小:", BatchNum)

	// 查询当前表存在的数据日期
	sqlstr := t.Sql(tableau, interval, datecloum, cloumtype, "1=1")
	zlog.Debug("生成查询SQL:", sqlstr)

	zlog.Debug("开始查询分组日期")
	df, err := t.Conn.ReadSql(sqlstr)
	if err != nil {
		Re.Status = QueryLastDate
		zlog.Error("查询分组日期失败，任务名:", tableau, "SQL:", sqlstr, "错误:", err.Error())
		return
	}
	zlog.Debug("查询分组日期完成，任务名:", tableau, "结果数量:", len(df))
	// 根据配置的interval判断是否需要归档
	if len(df) == 0 {
		SetProgress(tableau, 100.0)
		zlog.Debug("任务进度设置为100%，任务名:", tableau, "原因: 无数据需要归档")
		zlog.Debug("未找到需要归档的数据，任务完成，任务名:", tableau)
		return
	} else if len(df) > 0 && utils.IsAfterDaysAgo(df[0][datecloum], interval) {
		SetProgress(tableau, 100.0)
		zlog.Debug("任务进度设置为100%，任务名:", tableau, "原因: 数据在", interval, "天内，过新无需归档")
		zlog.Debug("数据都是", interval, "天内的新数据，无需归档，任务完成，任务名:", tableau)
		return
	} else {
		// 有旧数据需要归档，计算实际进度
		oldProgress := GetProgress(tableau)
		newProgress := t.Conn.TaskProgress(tableau, dbname)
		SetProgress(tableau, newProgress)
		zlog.Debug("【Row模式】发现需要归档的旧数据，任务名:", tableau)
		zlog.Debug("【Row模式】进度更新: 旧进度:", oldProgress, "% → 新进度:", newProgress, "%")
		zlog.Debug("【Row模式】数据条数:", len(df), "间隔天数:", interval)
	}

	zlog.Debug("开始处理归档数据，任务名:", tableau, "数据条数:", len(df))
	totalDates := len(df)
	for i, v := range df {
		zlog.Debug("处理第", i+1, "/", totalDates, "条归档数据，任务名:", tableau)
		// datetime类型需要格式化下
		zlog.Debug("处理日期列值:", v[datecloum])
		dateStr := strings.Replace(v[datecloum], "-", "", -1)
		if len(dateStr) < 6 {
			zlog.Error("日期格式错误，长度不足6位:", dateStr, "表名:", tableau)
			continue
		}
		vDatecloum := dateStr[:6]
		targetTable := tableau + "_" + vDatecloum

		// 更新进度 - 根据已处理的日期数计算进度
		currentProgress := float64(i) / float64(totalDates) * 100.0
		oldProgress := GetProgress(tableau)
		if UpdateProgress(tableau, currentProgress) {
			zlog.Debug("【Row模式】更新归档进度，表名:", tableau, "旧进度:", oldProgress, "% → 新进度:", currentProgress, "%")
			zlog.Debug("【Row模式】处理进度: 已处理", i, "/", totalDates, "个日期")
		} else {
			zlog.Debug("【Row模式】跳过进度更新，表名:", tableau, "当前进度:", oldProgress, "% 计算进度:", currentProgress, "%")
		}

		// 对比表结构
		zlog.Debug("开始克隆表结构，源表:", tableau, "目标表:", targetTable)
		t.Conn.CloningTable(tableau, targetTable)

		zlog.Debug("开始同步表结构，源表:", tableau, "目标表:", targetTable)
		ok := t.Conn.AlterColumn(tableau, targetTable)
		// 目标表结构一致
		if ok {
			zlog.Debug("表结构同步成功，开始数据迁移，目标表:", targetTable)
			// 生成插入数列表
			sql2 := t.Sql2(tableau, datecloum, cloumtype, PRIMARY, v[datecloum])
			zlog.Debug("sql2", sql2)
			// 主键列表
			rows, err := t.Conn.ReadSql(sql2)
			if err != nil {
				Re.Status = QueryLastDate
				Re.StatusMap["Query"] = 1
				zlog.Debug("查询分组日期失败")
			} else {
				Re.Status = 0
				Re.StatusMap["Query"] = 0
			}
			// 主键保存到列表中方便核对
			for n, row := range utils.Batch(rows, BatchNum) {
				zlog.Debug("row:", row)
				cond := utils.Cond(row)
				zlog.Debug("cond:", cond)
				sql := t.InsertStr(tableau, tableau+"_"+vDatecloum, datecloum, v[datecloum], cloumtype, cond)
				zlog.Debug("InsertStr:", sql)
				if ok := t.Conn.SqlAction(sql); ok {
					Re.Status = 0
					Re.StatusMap["Insert"] = 0
					zlog.Debug("插入成功", sql)
					zlog.Debug("DelStr:", t.DelStr(tableau, datecloum, v[datecloum], cloumtype, cond))
					_, err := t.Conn.Db.Exec(t.DelStr(tableau, datecloum, v[datecloum], cloumtype, cond))
					if err != nil {
						Re.Status = Del
						Re.StatusMap["Del"] = 1
						zlog.Error("删除失败", tableau, len(row), err.Error())
					} else {
						Re.Status = 0
						Re.StatusMap["Del"] = 0
						zlog.Debug("删除成功", tableau, len(row))
						// 更新表统计信息
						t.Conn.AnalyzeTable(tableau)
						t.Conn.AnalyzeTable(tableau + "_" + vDatecloum)
					}
				} else {
					Re.Status = Insert
					Re.StatusMap["Insert"] = 1
					zlog.Error("插入失败", tableau+"_"+vDatecloum, n)
				}
			}
			Re.Status = 0
			Re.StatusMap["Removal"] = 0
		} else {
			Re.Status = NotAlterColumn
			Re.StatusMap["Removal"] = 1
			zlog.Error(tableau, "->", tableau+"_"+vDatecloum+"结构无法修正")
		}
	}

	// 单次Row任务完成，但不设置为100%，让上层逻辑判断是否真正完成
	oldProgress := GetProgress(tableau)
	zlog.Debug("【Row模式】单次任务完成，表名:", tableau, "当前进度:", oldProgress, "% (不强制设置为100%)")
	zlog.Debug("【Row模式】等待上层逻辑检查是否还有数据需要归档")
}

func (t *Row) DelStr(desTableau, datecloum, vDatecloum, cloumtype, Conditions string) string {
	if cloumtype == "datetime" {
		if len(vDatecloum) < 8 {
			zlog.Error("日期格式错误，长度不足8位:", vDatecloum, "表名:", desTableau)
			return ""
		}
		vDatecloum = vDatecloum[0:4] + "-" + vDatecloum[4:6] + "-" + vDatecloum[6:8]
		return fmt.Sprintf(`
          delete
          from
              %s
          where
              %s between '%s 00:00:00' and '%s 23:59:59'
              and %s`, desTableau, datecloum, vDatecloum, vDatecloum, Conditions)
	} else {
		return fmt.Sprintf(`
          delete
          from
              %s
          where
              %s = %s
              and %s`, desTableau, datecloum, vDatecloum, Conditions)
	}
}

func (t *Row) InsertStr(srcTableau, desTableau, datecloum, vDatecloum, cloumtype, Conditions string) string {
	columns, err := t.Conn.GetColumnNames(srcTableau, "gstation")
	if err != nil {
		fmt.Println("Failed to get column names:", err)
	}
	columnsStr := strings.Join(columns, ",")
	if cloumtype == "datetime" {
		vDatecloum = vDatecloum[0:4] + "-" + vDatecloum[4:6] + "-" + vDatecloum[6:8]
		return fmt.Sprintf(`
          insert
              ignore
          into
             %s (%s)
          select
              %s
          from
              %s
          where
              %s between '%s 00:00:00' and '%s 23:59:59'
              and %s`, desTableau, columnsStr, columnsStr, srcTableau, datecloum, vDatecloum, vDatecloum, Conditions)
	} else {
		return fmt.Sprintf(`
          insert
              ignore
          into
             %s (%s)
          select
              %s
          from
              %s
          where
              %s = %s
              and %s`, desTableau, columnsStr, columnsStr, srcTableau, datecloum, vDatecloum, Conditions)
	}
}

// Sql 查询未备份数据的日期
func (t *Row) Sql(tableau string, interval int, datecloum, cloumtype, conditions string) string {
	zlog.Debug("Sql", tableau, interval, datecloum, cloumtype)
	if cloumtype == "datetime" {
		return fmt.Sprintf(`
           select
               REPLACE(substring(%s, 1, 10),'-', '') as %s
           from
               %s
           where
               %s < DATE_SUB(curdate(),interval %d day)
               and length(%s) >= 19
               and %s
           order by null limit 1`, datecloum, datecloum, tableau, datecloum, interval, datecloum, conditions)
	} else {
		return fmt.Sprintf(`
           select
               %s
           from
               %s
           where
               %s < DATE_FORMAT(DATE_SUB(curdate(),interval %d day),'%%Y%%m%%d')
               and length(%s) >= 8
               and %s
           order by null limit 1`, datecloum, tableau, datecloum, interval, datecloum, conditions)
	}
}

// Sql2 查询备份日期的记录
func (t *Row) Sql2(tableau, datecloum, cloumtype, PRIMARY, vDatecloum string) string {
	zlog.Debug("v_datecloum", tableau, datecloum, cloumtype, vDatecloum)
	if cloumtype == "datetime" {
		vDatecloum = vDatecloum[0:4] + "-" + vDatecloum[4:6] + "-" + vDatecloum[6:8]
		return fmt.Sprintf(`
           select
               %s
           from
               %s
           where
               %s between '%s 00:00:00' and '%s 23:59:59'
           order by null limit 10000`, PRIMARY, tableau, datecloum, vDatecloum, vDatecloum)
	} else {
		return fmt.Sprintf(`
           select
               %s
           from
               %s
           where
               %s = %s
           order by null limit 10000`, PRIMARY, tableau, datecloum, vDatecloum)
	}
}

func (t *Row) CreateNewTable(tableau string) {
	zlog.Debug("进入Row.CreateNewTable方法，表名:", tableau)

	// 判断配置是否存在
	zlog.Debug("检查配置是否存在，表名:", tableau)
	if _, ok := GetBackinfo(tableau); !ok {
		allBackinfo := GetAllBackinfo()
		zlog.Error("配置不存在，表名:", tableau, "Backinfo长度:", len(allBackinfo))
		Re.Status = ReadConfig
		zlog.Error("获取配置失败，程序退出，表名:", tableau)
		os.Exit(1)
	}
	zlog.Debug("配置检查通过，表名:", tableau)

	// 判断备份表是否存在
	zlog.Debug("检查备份表是否存在，表名:", tableau)
	if !t.Conn.TableExist(tableau) {
		zlog.Debug("备份表不存在，开始创建表，表名:", tableau)
		// 创建表
		tableSchema := t.Conn.Tableschme(tableau)
		zlog.Debug("获取表结构完成，表名:", tableau, "结构长度:", len(tableSchema))
		if t.Conn.Createtable(tableSchema) {
			zlog.Debug("创建表成功，表名:", tableau)
		} else {
			zlog.Error("创建表失败，表名:", tableau)
			Re.Status = CreateTbale
		}
	} else {
		zlog.Debug("备份表已存在，跳过创建，表名:", tableau)
	}
	zlog.Debug("退出Row.CreateNewTable方法，表名:", tableau)
}

func (t *Row) Start(jobinfo *JobInfo) {
	zlog.Debug("【关键点】进入Row.Start方法，任务名:", jobinfo.CronJob.JobName, "模式: row")
	zlog.Debug("【关键点】检查任务运行状态，Re.Running:", Re.Running)

	if Re.Running {
		zlog.Debug("任务运行状态为true，开始执行row任务:", jobinfo.CronJob.JobName)

		zlog.Debug("步骤1: 开始创建新表，表名:", jobinfo.CronJob.JobName)
		t.CreateNewTable(jobinfo.CronJob.JobName)
		zlog.Debug("步骤1: 创建新表完成，表名:", jobinfo.CronJob.JobName)

		zlog.Debug("步骤2: 开始同步表结构，表名:", jobinfo.CronJob.JobName)
		zlog.Info("【表结构同步】Row模式开始同步表结构，表名:", jobinfo.CronJob.JobName)
		t.Conn.SyncTable(jobinfo.CronJob.JobName)
		zlog.Info("【表结构同步】Row模式表结构同步完成，表名:", jobinfo.CronJob.JobName)
		zlog.Debug("步骤2: 同步表结构完成，表名:", jobinfo.CronJob.JobName)

		zlog.Debug("步骤3: 开始运行row迁移逻辑，表名:", jobinfo.CronJob.JobName)
		t.Run(jobinfo)
		zlog.Debug("步骤3: row迁移逻辑运行完成，表名:", jobinfo.CronJob.JobName)

		zlog.Debug("Row任务全部完成，任务名:", jobinfo.CronJob.JobName)
	} else {
		zlog.Debug("任务运行状态为false，跳过执行，任务名:", jobinfo.CronJob.JobName)
	}
	zlog.Debug("退出Row.Start方法，任务名:", jobinfo.CronJob.JobName)
}

func (t *Row) Close() {
	return
}

func (t *Row) CloseConn() {
	t.Conn.Close()
}
