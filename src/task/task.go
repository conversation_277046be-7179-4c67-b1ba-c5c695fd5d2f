package task

import (
	"context"
	pubconn "mysql_archive/pkg/conn"
)

var (
	ctx, cancel = context.WithCancel(context.Background())
)

type Task struct {
	cancel context.CancelFunc
	ctx    context.Context
	Conn   *pubconn.Conn
}

// Loopup 数据库重连
func (t *Task) Loopup() {
	t.Conn.Db.Stats()
}

//func init() {
//    db, err := pubconn.Init(*Mysqldb)
//    if err != nil {
//        logger.Debug("连接数据库失败", err.Error())
//    }
//    Conn := &pubconn.Conn{
//        Db: db,
//    }
//    go func(Conn *pubconn.Conn) {
//        for {
//            // 保持数据库连接
//            Conn.LoopConn()
//            // 更新进度
//            Conn.TaskProgress()
//            time.Sleep(10 * time.Second)
//        }
//    }(Conn)
//}
