package sync

import (
	"database/sql"
	"fmt"
	. "mysql_archive/pkg/config"
	pubconn "mysql_archive/pkg/conn"
	"mysql_archive/pkg/zlog"
	"os"
)

type Sync struct {
	Conn *pubconn.Conn
}

var (
	df  []map[string]string
	db  *sql.DB
	Err error
)

// RsyncSchema 同步表结构
func (t *Sync) Run(Backinfo *JobInfo) {
	tableau := Backinfo.CronJob.JobName
	zlog.Debug("进入Sync.Run方法，任务名:", tableau)

	// 查询相关表
	querySQL := fmt.Sprintf("show tables like '%s%%'", tableau+"_2")
	zlog.Debug("开始查询相关表，SQL:", querySQL)
	df, Err = t.Conn.ReadSql(querySQL)

	if Err != nil {
		zlog.Error("查询相关表失败，任务名:", tableau, "错误:", Err.Error())
		Re.Status = ReadRaw
		Re.StatusMap["RsyncSchema"] = 1
		SetSyncStatus(tableau, SyncFailQueryTables, GetSyncStatusMessage(SyncFailQueryTables))
		zlog.Debug("设置状态为ReadRaw，任务名:", tableau)
		return
	} else {
		zlog.Debug("查询相关表成功，任务名:", tableau, "找到表数量:", len(df))
		Re.Status = 0
		Re.StatusMap["RsyncSchema"] = 0
	}

	if len(df) == 0 {
		zlog.Debug("未找到相关表，任务完成，任务名:", tableau)
		SetSyncStatus(tableau, SyncSuccessNoChanges, GetSyncStatusMessage(SyncSuccessNoChanges))
		return
	}

	zlog.Debug("开始遍历相关表进行结构同步，任务名:", tableau)
	for i, row := range df {
		zlog.Debug("处理第", i+1, "个表，任务名:", tableau)
		for columnName, Destableau := range row {
			zlog.Debug("开始同步表结构，源表:", tableau, "目标表:", Destableau, "列名:", columnName)
			ok := t.Conn.AlterColumn(tableau, Destableau)
			// 目标表结构一致
			if ok {
				zlog.Debug("表结构同步成功，源表:", tableau, "目标表:", Destableau)
				Re.Status = 0
				Re.StatusMap["RsyncSchema"] = 0
				SetSyncStatus(tableau, SyncSuccessExisting, GetSyncStatusMessage(SyncSuccessExisting))
				zlog.Debug("Sync.Run方法执行成功完成，任务名:", tableau)
				return
			} else {
				zlog.Error("表结构同步失败，源表:", tableau, "目标表:", Destableau)
				Re.Status = NotAlterColumn
				Re.StatusMap["RsyncSchema"] = 1
				SetSyncStatus(tableau, NotAlterColumn, GetSyncStatusMessage(NotAlterColumn))
				zlog.Error(tableau, "->", Destableau+"结构无法修正")
			}
		}
	}
	zlog.Debug("退出Sync.Run方法，任务名:", tableau)
}

func (t *Sync) CreateNewTable(tableau string) {
	zlog.Debug("进入CreateNewTable方法，表名:", tableau)

	// 判断配置是否存在
	zlog.Debug("检查配置是否存在，表名:", tableau)
	if _, ok := Backinfo[tableau]; !ok {
		zlog.Error("配置不存在，表名:", tableau, "Backinfo长度:", len(Backinfo))
		Re.Status = ReadConfig
		SetSyncStatus(tableau, SyncFailConfigMissing, GetSyncStatusMessage(SyncFailConfigMissing))
		zlog.Error("获取配置失败，程序退出，表名:", tableau)
		os.Exit(1)
	}
	zlog.Debug("配置检查通过，表名:", tableau)

	// 判断备份表是否存在
	zlog.Debug("检查备份表是否存在，表名:", tableau)
	if !t.Conn.TableExist(tableau) {
		zlog.Debug("备份表不存在，开始创建表，表名:", tableau)
		// 创建表
		tableSchema := t.Conn.Tableschme(tableau)
		zlog.Debug("获取表结构完成，表名:", tableau, "结构长度:", len(tableSchema))
		if t.Conn.Createtable(tableSchema) {
			zlog.Debug("创建表成功，表名:", tableau)
			SetSyncStatus(tableau, SyncSuccessNewTable, GetSyncStatusMessage(SyncSuccessNewTable))
		} else {
			zlog.Error("创建表失败，表名:", tableau)
			Re.Status = CreateTbale
			SetSyncStatus(tableau, SyncFailTempCreate, GetSyncStatusMessage(SyncFailTempCreate))
		}
	} else {
		zlog.Debug("备份表已存在，跳过创建，表名:", tableau)
		SetSyncStatus(tableau, SyncSuccessExisting, GetSyncStatusMessage(SyncSuccessExisting))
	}
	zlog.Debug("退出CreateNewTable方法，表名:", tableau)
}

func (t *Sync) Start(jobinfo *JobInfo) {
	zlog.Debug("【关键点】 进入Sync.Start方法，任务名:", jobinfo.CronJob.JobName, "模式: sync")
	zlog.Debug("【关键点】检查任务运行状态，Re.Running:", Re.Running)

	if Re.Running {
		zlog.Debug("任务运行状态为true，开始执行sync任务:", jobinfo.CronJob.JobName)

		zlog.Debug("步骤1: 开始创建新表，表名:", jobinfo.CronJob.JobName)
		t.CreateNewTable(jobinfo.CronJob.JobName)
		zlog.Debug("步骤1: 创建新表完成，表名:", jobinfo.CronJob.JobName)

		zlog.Debug("步骤2: 开始同步表结构，表名:", jobinfo.CronJob.JobName)
		t.Conn.SyncTable(jobinfo.CronJob.JobName)
		zlog.Debug("步骤2: 同步表结构完成，表名:", jobinfo.CronJob.JobName)

		zlog.Debug("步骤3: 开始运行同步逻辑，表名:", jobinfo.CronJob.JobName)
		t.Run(jobinfo)
		zlog.Debug("步骤3: 同步逻辑运行完成，表名:", jobinfo.CronJob.JobName)

		zlog.Debug("Sync任务全部完成，任务名:", jobinfo.CronJob.JobName)
	} else {
		zlog.Debug("任务运行状态为false，跳过执行，任务名:", jobinfo.CronJob.JobName)
	}
	zlog.Debug("退出Sync.Start方法，任务名:", jobinfo.CronJob.JobName)
}

func (t *Sync) Close() {
	return
}

func (t *Sync) CloseConn() {
	t.Conn.Close()
}
