# MySQL Archive 版本管理

## 版本信息

当前版本：**v1.2.0**

### 版本号规则

采用语义化版本控制（Semantic Versioning）：`MAJOR.MINOR.PATCH`

- **MAJOR**: 主版本号，不兼容的API修改
- **MINOR**: 次版本号，向下兼容的功能性新增
- **PATCH**: 修订版本号，向下兼容的问题修正

### 版本信息字段

```json
{
  "major": 1,           // 主版本号
  "minor": 2,           // 次版本号  
  "patch": 0,           // 修订版本号
  "build": "v1.2.0-dirty",     // 构建版本（包含Git状态）
  "build_time": "2025-07-15 13:20:53",  // 构建时间
  "git_commit": "1d409b2",     // Git提交哈希
  "version_string": "v1.2.0-dirty"      // 完整版本字符串
}
```

## 查看版本信息

### 1. 命令行方式

```bash
# 完整版本信息
./mysql_archive_local -version

# 简写方式
./mysql_archive_local -v
```

输出示例：
```
MySQL Archive v1.2.0-dirty
构建版本: v1.2.0-dirty
构建时间: 2025-07-15 13:20:53
Git提交: 1d409b2
版本详情: v1.2.0
```

### 2. API方式

#### 专用版本API
```bash
curl http://127.0.0.1:14445/api/version
```

#### 主状态API中的版本信息
```bash
curl http://127.0.0.1:14445/api/information/v1/mtriecs
```

## 构建和版本管理

### 使用Makefile构建

```bash
# 构建本地版本
make build-local

# 构建并显示版本信息
make version

# 开发模式运行
make dev

# 构建Linux版本
make build-linux
make build-linux-arm

# 清理构建文件
make clean

# 查看帮助
make help
```

### 使用构建脚本

```bash
# 使用构建脚本
./build.sh
```

### 版本信息注入

构建时通过`-ldflags`参数注入版本信息：

```bash
go build -ldflags "
  -X 'mysql_archive/pkg/config.BuildVersion=v1.2.0'
  -X 'mysql_archive/pkg/config.BuildTime=2025-07-15 13:20:53'
  -X 'mysql_archive/pkg/config.GitCommit=1d409b2'
" -o mysql_archive_local main.go
```

## 版本状态说明

### 构建状态标识

- **clean**: 基于干净的Git工作目录构建
- **dirty**: 包含未提交的更改
- **dev**: 开发版本（默认值）

### 版本获取逻辑

1. 尝试从Git标签获取版本号：`git describe --tags --abbrev=0`
2. 如果没有标签，使用默认版本：`v1.2.0`
3. 检查工作目录状态，如有未提交更改则添加`-dirty`后缀
4. 获取Git提交哈希：`git rev-parse --short HEAD`
5. 记录构建时间

## 版本历史

### v1.2.0 (2025-07-15)
-  增加版本管理系统
-  支持命令行版本查询（-version, -v）
-  增加版本信息API端点（/api/version）
-  在主状态API中包含详细版本信息
-  支持构建时版本信息注入
-  增加Makefile和构建脚本
-  增加debug日志动态控制功能

### v1.1.x (历史版本)
- 基础功能实现
- 支持sync/day/row三种归档模式
- 表结构自动同步
- 任务调度系统

## 开发指南

### 更新版本号

1. 修改`pkg/config/config.go`中的版本常量：
   ```go
   const (
       VERSION_MAJOR = 1
       VERSION_MINOR = 2
       VERSION_PATCH = 1  // 更新这里
   )
   ```

2. 创建Git标签（可选）：
   ```bash
   git tag v1.2.1
   git push origin v1.2.1
   ```

3. 重新构建程序：
   ```bash
   make build-local
   ```

### 发布流程

1. 更新版本号
2. 更新CHANGELOG
3. 创建Git标签
4. 构建发布版本
5. 测试版本信息
6. 部署到生产环境

## 兼容性说明

- 保持`version`字段（int64类型）用于向后兼容
- 新增`version_info`字段提供详细版本信息
- API响应格式保持向下兼容
