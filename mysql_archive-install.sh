#!/usr/bin/env bash
# curl -s -k https://**********/service/script/categraf-install.sh|sh -s install v0.3.70
source /etc/profile
Lockfile='/tmp/categraf.pid'
# 进程pid写入
function wpid(){
  # 判断是否有写入权限
  if [ ! -w "$Lockfile" ]; then
      chmod +w "$Lockfile"
      echo "$$" > "$Lockfile"
  else
     echo "$$" > "$Lockfile"
  fi
}

# 判断进程是否已经运行
function exist_check() {
	# 判断文件是否存在
	if [ -f "$Lockfile" ]; then
		# 判断pid是否为空
		pid=$(cat $Lockfile)
		# shellcheck disable=SC2236
		if [ ! -z "$pid" ]; then
			# 判断进程是否存在
			# shellcheck disable=SC2009
			# shellcheck disable=SC2126
			ProcNumber=$(ps -ef | grep -w "$pid" | grep -v grep | wc -l)
			if [ "$ProcNumber" -le 0 ]; then
				echo '程序没有运行'
				wpid
			else
				echo '程序已运行'
				exit 0
			fi
		fi
	else
		touch "$Lockfile"
		wpid
	fi
}

function download_and_verify() {
    ARCH=$(uname -m)
    FILE=""
    MD5_URL=""
    VERSION="$1"

    if [[ $ARCH == *"x86"* ]]; then
        FILE="categraf-"$VERSION"-linux-amd64.tar.gz"
    elif [[ $ARCH == *"aarch"* ]]; then
        FILE="categraf-"$VERSION"-linux-arm.tar.gz"
    else
        echo "Unsupported architecture: $ARCH"
        exit 1
    fi

    FILE_URL="https://**********/service/package/mysql_archive/${FILE}"
    MD5_URL="${FILE_URL}.md5"

    for attempt in {1..2}; do
        echo "Attempt $attempt: Downloading ${MD5_URL}..."
        curl -k -s -O ${MD5_URL}

        REMOTE_MD5=$(cat ${FILE}.md5 | awk '{print $1}')
        echo "Remote MD5: $REMOTE_MD5"

        if [ -f "$FILE" ]; then
            echo "文件已存在。正在验证MD5..."
        else
            echo "Downloading ${FILE_URL}..."
            curl -k -s -O ${FILE_URL}
        fi

        LOCAL_MD5=$(md5sum ${FILE} | awk '{print $1}')
        echo "Local MD5: $LOCAL_MD5"

        if [ "$LOCAL_MD5" == "$REMOTE_MD5" ]; then
            echo "MD5验证成功。"
            return 0
        else
            echo "MD5验证失败。"
            rm -f ${FILE} # 删除文件以便重试
        fi
    done

    echo "MD5验证失败，已达最大重试次数。"
    exit 1
}


function install() {
    # 指定service文件的URL
    SERVICE_URL="https://**********/service/package/categraf/categraf.service"
    SERVICE_FILE="categraf.service"

    # 解压文件到/usr/local
    echo "Extracting ${FILE} to /usr/local..."
    rm -rf /usr/local/categraf
    tar --no-xattrs -xvf ${FILE} -C /usr/local

    # 下载service文件
    echo "Downloading ${SERVICE_URL}..."
    curl -k -s -o /usr/lib/systemd/system/${SERVICE_FILE} ${SERVICE_URL}

    # 重新加载systemd守护进程
    echo "Reloading the systemd daemon..."
    systemctl daemon-reload

    # 启用服务
    echo "Enabling the categraf service..."
    systemctl enable categraf.service

    # 重启服务
    echo "Restarting the categraf service..."
    systemctl restart categraf.service

    echo "Installation and service setup complete."
}

function uninstall() {
    SERVICE_FILE="/usr/lib/systemd/system/categraf.service"
    CATEGRAF_DIR="/usr/local/categraf"

    # 检查服务是否存在并停止、禁用
    if systemctl --quiet is-enabled categraf.service; then
        echo "Stopping and disabling categraf service..."
        systemctl disable categraf.service
        systemctl stop categraf.service
    else
        if pgrep -f categraf >/dev/null; then
            echo "Categraf process exists even though the service is not enabled. Consider manual intervention."
            # 你可以在这里加入杀死进程的命令，例如:
            pkill -f categraf
        else
            echo "Categraf service does not exist or is already disabled, and no process is running."
        fi
    fi

    # 删除服务文件
    if [ -f "$SERVICE_FILE" ]; then
        echo "Removing service file..."
        rm -f "$SERVICE_FILE"
    else
        echo "Service file does not exist."
    fi

    # 重新加载systemd守护进程以应用更改
    systemctl daemon-reload

    # 删除categraf目录
    if [ -d "$CATEGRAF_DIR" ]; then
        echo "Removing categraf directory..."
        rm -rf "$CATEGRAF_DIR"
    else
        echo "Categraf directory does not exist."
    fi

    echo "Uninstallation of categraf complete."
}

function clear() {
  rm -rf categraf*gz*
}

case "$1" in
  install)
  exist_check
  uninstall
  download_and_verify $2
  install
  clear
    ;;
  uninstall)
    exist_check
    uninstall
    ;;
  *)
    echo "Usage: {install|uninstall}"
esac
exit 0
