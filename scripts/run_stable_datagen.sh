#!/bin/bash

# 稳定版数据生成脚本

echo "=== MySQL Archive 稳定版数据生成工具 ==="
echo ""

# 设置 Go 环境
export GOROOT=/Users/<USER>/go/go1.23.7
export GOPATH=/Users/<USER>/go
export GOPROXY=https://goproxy.cn,direct
export PATH=$GOROOT/bin:$PATH

# 进入项目根目录
cd "$(dirname "$0")/.."

echo "开始生成数据..."
echo "目标表: a, b, c"
echo "每表记录数: 1,000,000 条"
echo "日期范围: 30 天"
echo "特点: 逐条插入，避免死锁"
echo ""

# 运行稳定版数据生成器
go run cmd/stable_datagen/main.go

echo ""
echo "数据生成完成！"
