#!/bin/bash

# API 状态监控脚本

API_URL="http://127.0.0.1:14445/api/information/v1/mtriecs"
MONITOR_DURATION=300  # 监控5分钟
INTERVAL=10           # 每10秒检查一次

echo "=== MySQL Archive API 状态监控 ==="
echo "监控时长: ${MONITOR_DURATION}秒"
echo "检查间隔: ${INTERVAL}秒"
echo "开始时间: $(date)"
echo ""

# 记录初始状态
echo "=== 初始状态 ==="
curl -s "$API_URL" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    print('时间:', '$(date +%H:%M:%S)')
    print('系统状态: 运行={}, Debug={}, 状态码={}'.format(
        data['Re']['running'], data['Re']['debug_log'], data['Re']['status']))
    print('进度状态:')
    for table, progress in data['Progress'].items():
        print(f'  {table}: {progress}%')
    print()
except Exception as e:
    print('解析错误:', e)
"

echo ""
echo "=== 开始监控变化 ==="

# 监控循环
start_time=$(date +%s)
previous_state=""

while true; do
    current_time=$(date +%s)
    elapsed=$((current_time - start_time))

#    if [ $elapsed -ge $MONITOR_DURATION ]; then
#        echo "监控完成，总时长: ${elapsed}秒"
#        break
#    fi

    # 获取当前状态
    current_state=$(curl -s "$API_URL" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    # 只关注关键变化
    progress = data['Progress']
    sync_status = data['Sync']

    # 格式化输出
    result = []
    result.append(f'时间: $(date +%H:%M:%S)')

    # 进度变化
    progress_line = '进度: '
    for table in ['a', 'b', 'c']:
        if table in progress:
            progress_line += f'{table}={progress[table]}% '
    result.append(progress_line)

    # 状态变化
    status_line = '状态: '
    for table in ['a', 'b', 'c']:
        if table in sync_status:
            status_line += f'{table}={sync_status[table][\"code\"]} '
    result.append(status_line)

    print('|'.join(result))

except Exception as e:
    print('ERROR: 解析失败')
" 2>/dev/null)

    # 检查是否有变化
    if [ "$current_state" != "$previous_state" ]; then
        echo " [变化检测] $current_state"
        previous_state="$current_state"
    else
        echo " [$(date +%H:%M:%S)] 无变化 (已监控 ${elapsed}s)"
    fi

    sleep $INTERVAL
done

echo ""
echo "=== 最终状态 ==="
curl -s "$API_URL" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    print('结束时间:', '$(date)')
    print('最终进度:')
    for table, progress in data['Progress'].items():
        print(f'  {table}: {progress}%')
    print('最终Sync状态:')
    for table, sync_info in data['Sync'].items():
        if table in ['a', 'b', 'c']:
            print(f'  {table}: 状态码={sync_info[\"code\"]}, 消息={sync_info[\"message\"]}')
except Exception as e:
    print('解析错误:', e)
"
