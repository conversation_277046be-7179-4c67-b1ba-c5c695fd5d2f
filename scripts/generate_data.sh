#!/bin/bash

# MySQL Archive 数据生成脚本
# 为 a、b、c 表各生成 100 万条数据，日期覆盖 30 天

set -e

echo "=== MySQL Archive 数据生成工具 ==="
echo ""

# 检查 Go 环境
if ! command -v go &> /dev/null; then
    echo "错误: 未找到 Go 环境，请先安装 Go"
    exit 1
fi

# 进入项目根目录
cd "$(dirname "$0")/.."

echo "当前目录: $(pwd)"
echo ""

# 设置 Go 环境变量
export GOROOT=/Users/<USER>/go/go1.23.7
export GOPATH=/Users/<USER>/go
export GOPROXY=https://goproxy.cn,direct
export PATH=$GOROOT/bin:$PATH

echo "Go 环境配置:"
echo "  GOROOT: $GOROOT"
echo "  GOPATH: $GOPATH"
echo "  GOPROXY: $GOPROXY"
echo ""

# 检查数据库连接
echo "检查数据库连接..."
mysql -h127.0.0.1 -P3306 -uhsyq -pHsyq@123 -e "SELECT 1" gstation 2>/dev/null || {
    echo "警告: 数据库连接失败，请确保 MySQL 服务正在运行"
    echo "数据库配置: 127.0.0.1:3306, 用户: hsyq, 数据库: gstation"
    echo ""
}

# 显示配置信息
echo "数据生成配置:"
echo "  目标表: a, b, c"
echo "  每表记录数: 1,000,000 条"
echo "  日期范围: 30 天"
echo "  批量大小: 1,000 条"
echo "  并发数: 10 个协程"
echo ""

# 询问是否清空表
read -p "是否在生成前清空表数据? (y/N): " clear_tables
echo ""

# 开始生成数据
echo "开始生成数据..."
echo "预计耗时: 5-10 分钟（取决于硬件性能）"
echo ""

if [[ "$clear_tables" =~ ^[Yy]$ ]]; then
    echo "将清空表数据后重新生成..."
    go run cmd/datagen/main.go --clear
else
    echo "将在现有数据基础上追加..."
    go run cmd/datagen/main.go
fi

echo ""
echo "=== 数据生成完成 ==="
