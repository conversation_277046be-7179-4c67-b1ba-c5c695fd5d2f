package main

import (
	"flag"
	"fmt"
	. "mysql_archive/agent"
	"mysql_archive/pkg/config"
	"os"
	"strings"
)

func main() {
	var showVersion = flag.Bool("version", false, "显示版本信息")
	var logLevel = flag.String("log-level", "warn", "设置日志级别 (debug, info, warn, error)")
	flag.Parse()

	if *showVersion {
		versionInfo := config.GetVersionInfo()
		fmt.Printf("MySQL Archive %s\n", versionInfo.VersionString)
		os.Exit(0)
	}

	// 处理日志级别参数
	selectedLogLevel := *logLevel
	// 验证日志级别参数
	validLevels := []string{"debug", "info", "warn", "error"}
	selectedLogLevel = strings.ToLower(selectedLogLevel)
	isValidLevel := false
	for _, level := range validLevels {
		if selectedLogLevel == level {
			isValidLevel = true
			break
		}
	}

	if !isValidLevel {
		fmt.Printf("错误: 无效的日志级别 '%s'。支持的级别: %s\n", selectedLogLevel, strings.Join(validLevels, ", "))
		os.Exit(1)
	}

	// 设置环境变量供日志初始化使用
	switch selectedLogLevel {
	case "debug":
		os.Setenv("LOG_LEVEL", "debug")
	case "info":
		os.Setenv("LOG_LEVEL", "info")
	case "warn":
		os.Setenv("LOG_LEVEL", "warn")
	case "error":
		os.Setenv("LOG_LEVEL", "error")
	}

	fmt.Printf("MySQL Archive 启动中... (日志级别: %s)\n", selectedLogLevel)
	fmt.Printf("准备调用 agent.Run()...\n")

	Run()

	fmt.Printf("agent.Run() 执行完成\n")
}
