.SILENT:
.PHONY: build build-linux build-linux-arm build-local version dev clean help

# 版本信息
VERSION_TAG := $(shell git describe --tags --abbrev=0 2>/dev/null || echo "v1.2.0")
BUILD_TIME := $(shell date '+%Y-%m-%d %H:%M:%S')
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
BUILD_VERSION := $(VERSION_TAG)

# 检查是否有未提交的更改
ifneq ($(shell git diff-index --quiet HEAD -- 2>/dev/null; echo $$?), 0)
    BUILD_VERSION := $(BUILD_VERSION)-dirty
endif

# Go环境变量
GOROOT := /Users/<USER>/go/go1.23.7
GOPATH := /Users/<USER>/go
export GOROOT := $(GOROOT)
export GOPATH := $(GOPATH)
export PATH := $(GOROOT)/bin:$(PATH)

# 版本构建参数
VERSION_LDFLAGS := -X 'mysql_archive/pkg/config.BuildVersion=$(BUILD_VERSION)' \
                   -X 'mysql_archive/pkg/config.BuildTime=$(BUILD_TIME)' \
                   -X 'mysql_archive/pkg/config.GitCommit=$(GIT_COMMIT)'

# 完整构建参数
LDFLAGS := -s -w $(VERSION_LDFLAGS)

all: build-linux build-linux-arm

vendor:
	GOPROXY=https://goproxy.cn go mod vendor

# 本地构建（用于开发和测试）
build-local:
	@echo "构建信息:"
	@echo "  版本标签: $(VERSION_TAG)"
	@echo "  构建版本: $(BUILD_VERSION)"
	@echo "  构建时间: $(BUILD_TIME)"
	@echo "  Git提交: $(GIT_COMMIT)"
	@echo ""
	@echo "开始构建本地版本..."
	go build -ldflags "$(VERSION_LDFLAGS)" -o mysql_archive_local main.go
	@echo "构建完成！"

build-linux:
	@echo "构建Linux AMD64版本..."
	GOOS=linux GOARCH=amd64 go build -ldflags "$(LDFLAGS)" -o mysql_archive_linux_amd64

build-linux-arm:
	@echo "构建Linux ARM64版本..."
	GOOS=linux GOARCH=arm64 go build -ldflags "$(LDFLAGS)" -o mysql_archive_linux_arm64

# 显示版本信息
version: build-local
	./mysql_archive_local -version

# 开发模式运行
dev:
	go run -ldflags "$(VERSION_LDFLAGS)" main.go

# 清理构建文件
clean:
	rm -f mysql_archive_local mysql_archive_linux_amd64 mysql_archive_linux_arm64
	@echo "清理完成！"

# 帮助信息
help:
	@echo "MySQL Archive 构建工具"
	@echo ""
	@echo "可用命令:"
	@echo "  make build-local  - 构建本地版本（用于开发测试）"
	@echo "  make build-linux  - 构建Linux AMD64版本"
	@echo "  make build-linux-arm - 构建Linux ARM64版本"
	@echo "  make dev          - 开发模式运行"
	@echo "  make version      - 构建并显示版本信息"
	@echo "  make clean        - 清理构建文件"
	@echo "  make vendor       - 下载依赖"
	@echo "  make all          - 构建所有版本"
	@echo "  make help         - 显示此帮助信息"
