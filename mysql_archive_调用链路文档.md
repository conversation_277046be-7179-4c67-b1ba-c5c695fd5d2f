# MySQL Archive 系统调用链路文档

## 概述

本文档详细描述了MySQL Archive系统从调度器到各种执行模式（sync/day/row）的完整调用链路，包括每个关键节点的debug日志和error日志记录。

## 系统架构

```
调度器 (Scheduler) 
    ↓
任务分发 (Metrics)
    ↓
执行模式选择 (sync/day/row)
    ↓
具体执行逻辑
    ↓
数据库操作
```

## 完整调用链路

### 1. 调度器启动阶段

#### 1.1 调度器初始化
**文件**: `pkg/exr/exr.go`
**方法**: `ScheduleCrontab(ctx context.Context)`

**关键日志点**:
- 调度器启动
- 监听任务更新和上下文取消信号
- 任务表更新处理
- 调度器锁管理

**日志示例**:
```
调度器启动
调度器初始化完成，开始监听任务更新和上下文取消信号
收到新的调度表更新，任务数量:1
获取调度器锁，开始更新调度表
```

#### 1.2 任务启动
**方法**: `startJobs()`

**关键日志点**:
- 任务协程启动
- 执行时间检查
- 任务触发条件判断

**日志示例**:
```
开始启动所有调度任务，任务总数:1
准备启动任务:etc_renew_record下次执行时间:2025-07-15 15:00:00
任务协程已启动:etc_renew_record
```

### 2. 任务分发阶段

#### 2.1 Metrics函数
**文件**: `pkg/exr/exr.go`
**方法**: `Metrics(JobInfo *JobInfo)`

**功能**: 根据任务配置的执行模式分发到对应的处理器

**关键日志点**:
- 任务模式识别
- 模式分发
- 执行状态跟踪

**日志示例**:
```
进入Metrics函数，任务名:etc_renew_record执行模式:sync
开始执行sync模式任务:etc_renew_record
sync模式任务执行完成:etc_renew_record
```

**支持的模式**:
- `sync`: 表结构同步模式
- `day`: 按天归档模式  
- `row`: 按行归档模式

### 3. 配置管理

#### 3.1 任务配置转换
**方法**: `ConvertToJobDetail(jobs map[string]map[string]string)`

**功能**: 将配置转换为任务详情，处理模式设置

**关键日志点**:
- 配置解析
- 模式设置（优先使用全局Re.CMode）
- 任务参数验证

**日志示例**:
```
任务配置转换，任务名:etc_renew_record使用模式:sync全局模式:sync
```

#### 3.2 远程配置同步
**文件**: `src/task/sync/SyncConfig.go`
**方法**: `RemoteSyncConfig()`

**关键日志点**:
- 远程配置获取
- 配置持久化
- 表结构信息获取

**日志示例**:
```
进入RemoteSyncConfig方法，开始同步远程配置
获取本地IP地址: ***********
获取远程配置成功，配置长度: 174
表结构获取并存储成功，表名: etc_renew_record 结构长度: 1804
```

## 4. 执行模式详解

### 4.1 Sync模式 (表结构同步)

#### 4.1.1 Sync.Start方法
**文件**: `src/task/sync/SyncSchema.go`
**方法**: `Start(jobinfo *JobInfo)`

**执行步骤**:
1. 检查任务运行状态
2. 创建新表
3. 同步表结构
4. 运行同步逻辑

**关键日志点**:
```
进入Sync.Start方法，任务名: etc_renew_record
检查任务运行状态，Re.Running: true
步骤1: 开始创建新表，表名: etc_renew_record
步骤2: 开始同步表结构，表名: etc_renew_record
步骤3: 开始运行同步逻辑，表名: etc_renew_record
Sync任务全部完成，任务名: etc_renew_record
```

#### 4.1.2 CreateNewTable方法
**功能**: 检查并创建目标表

**执行流程**:
1. 检查配置是否存在
2. 检查备份表是否存在
3. 如果不存在则创建表

**关键日志点**:
```
进入CreateNewTable方法，表名: etc_renew_record
检查配置是否存在，表名: etc_renew_record
检查备份表是否存在，表名: etc_renew_record
备份表不存在，开始创建表，表名: etc_renew_record
创建表完成，表名: etc_renew_record
```

#### 4.1.3 SyncTable方法
**文件**: `pkg/conn/db.go`
**方法**: `SyncTable(tableau string)`

**功能**: 同步表结构到当前表

**执行流程**:
1. 清理临时表
2. 创建临时表
3. 同步表结构

**关键日志点**:
```
进入SyncTable方法，表名: etc_renew_record
开始清理临时表: etc_renew_record_tmp
临时表清理成功: etc_renew_record_tmp
开始创建临时表: etc_renew_record_tmp
同步表结构到当前表成功，从: etc_renew_record_tmp 到: etc_renew_record
```

#### 4.1.4 Sync.Run方法
**功能**: 执行同步逻辑

**执行流程**:
1. 查询相关表
2. 遍历表进行结构同步
3. 执行AlterColumn操作

**关键日志点**:
```
进入Sync.Run方法，任务名: etc_renew_record
开始查询相关表，SQL: show tables like 'etc_renew_record_2%'
查询相关表成功，任务名: etc_renew_record 找到表数量: 0
未找到相关表，任务完成，任务名: etc_renew_record
```

### 4.2 Day模式 (按天归档)

#### 4.2.1 Day.Start方法
**文件**: `src/task/day/day.go`
**方法**: `Start(jobinfo *JobInfo)`

**执行步骤**:
1. 检查任务运行状态
2. 创建新表
3. 同步表结构
4. 运行day迁移逻辑

**关键日志点**:
```
进入Day.Start方法，任务名: etc_renew_record
检查任务运行状态，Re.Running: true
步骤1: 开始创建新表，表名: etc_renew_record
步骤2: 开始同步表结构，表名: etc_renew_record
步骤3: 开始运行day迁移逻辑，表名: etc_renew_record
Day任务全部完成，任务名: etc_renew_record
```

#### 4.2.2 Day.Run方法
**功能**: 执行按天归档逻辑

**执行流程**:
1. 查询归档日期
2. 计算任务进度
3. 处理归档数据
4. 克隆表结构
5. 数据迁移

**关键日志点**:
```
进入Day.Run方法，任务名: etc_renew_record
任务参数 - 间隔:30 日期列:dt_create_tm 列类型:datetime
开始查询归档日期
查询归档日期完成，任务名: etc_renew_record 结果数量: 5
开始处理归档数据，任务名: etc_renew_record 数据条数: 5
```

### 4.3 Row模式 (按行归档)

#### 4.3.1 Row.Start方法
**文件**: `src/task/row/row.go`
**方法**: `Start(jobinfo *JobInfo)`

**执行步骤**:
1. 检查任务运行状态
2. 创建新表
3. 同步表结构
4. 运行row迁移逻辑

**关键日志点**:
```
进入Row.Start方法，任务名: etc_renew_record
检查任务运行状态，Re.Running: true
步骤1: 开始创建新表，表名: etc_renew_record
步骤2: 开始同步表结构，表名: etc_renew_record
步骤3: 开始运行row迁移逻辑，表名: etc_renew_record
Row任务全部完成，任务名: etc_renew_record
```

#### 4.3.2 Row.Run方法
**功能**: 执行按行归档逻辑

**执行流程**:
1. 查询分组日期
2. 计算任务进度
3. 处理归档数据
4. 批量数据迁移

**关键日志点**:
```
进入Row.Run方法，任务名: etc_renew_record
任务参数 - 间隔:30 日期列:dt_create_tm 批次大小:1000
开始查询分组日期
查询分组日期完成，任务名: etc_renew_record 结果数量: 3
开始处理归档数据，任务名: etc_renew_record 数据条数: 3
```

## 5. 数据库操作层

### 5.1 表操作

#### 5.1.1 TableExist - 表存在性检查
**方法**: `TableExist(tablename string)`

**关键日志点**:
```
进入TableExist方法，检查表是否存在: etc_renew_record
执行表存在性检查SQL: select 1 as xx from INFORMATION_SCHEMA.STATISTICS where TABLE_NAME = ?
表存在检查通过，表名: etc_renew_record
```

#### 5.1.2 Createtable - 创建表
**方法**: `Createtable(sql string)`

**关键日志点**:
```
进入Createtable方法
开始创建表，SQL长度: 1804
创建表SQL: CREATE TABLE IF NOT EXISTS `etc_renew_record` (...)
创建表成功
```

#### 5.1.3 DropTable - 删除表
**方法**: `DropTable(TableName string)`

**关键日志点**:
```
进入DropTable方法，表名: etc_renew_record_tmp
执行删除表SQL: drop table if exists etc_renew_record_tmp
删除表成功，表名: etc_renew_record_tmp
```

### 5.2 表结构操作

#### 5.2.1 AlterColumn - 修改表字段
**方法**: `AlterColumn(SrcName string, DestTable string)`

**功能**: 比较两个表的结构差异并执行修改

**执行流程**:
1. 查找表结构差异
2. 分析差异类型（ADD/CHANGE）
3. 生成修改SQL
4. 执行表结构修改

**关键日志点**:
```
进入AlterColumn方法，源表: etc_renew_record_tmp 目标表: etc_renew_record
开始查找表结构差异，源表: etc_renew_record_tmp 目标表: etc_renew_record
表结构差异查询结果: [] 结果数量: 0
未发现表结构差异，无需修改，源表: etc_renew_record_tmp 目标表: etc_renew_record
```

#### 5.2.2 Tableschme - 获取表结构
**方法**: `Tableschme(tablename string)`

**功能**: 从缓存中获取表结构定义

**关键日志点**:
```
进入Tableschme方法，查询表结构，表名: etc_renew_record
从缓存获取表结构成功，表名: etc_renew_record 结构长度: 1804
```

### 5.3 数据查询

#### 5.3.1 ReadSql - 执行SQL查询
**方法**: `ReadSql(sqls string)`

**功能**: 执行SQL查询并返回结果集

**执行流程**:
1. 执行SQL查询
2. 获取列信息
3. 扫描查询结果
4. 构建结果集
5. 关闭资源

**关键日志点**:
```
进入ReadSql方法，SQL长度: 38
执行SQL: show tables like 'etc_renew_record_2%'
SQL查询执行成功
获取查询结果列信息成功，列数: 1
数据扫描完成，总行数: 0
ReadSql方法执行完成，返回结果行数: 0
数据库查询结果集已关闭
```

### 5.4 连接管理

#### 5.4.1 数据库连接初始化
**方法**: `Init(mysqlbase MysqlBase)`

**关键日志点**:
```
进入Init方法，初始化数据库连接
数据库连接参数 - 主机:localhost 端口:3306 数据库:gstation
数据库连接初始化成功
```

#### 5.4.2 连接状态检查
**方法**: `LoopConn()`

**关键日志点**:
```
检查数据库连接状态
数据库连接状态正常
```

## 6. 错误处理和状态管理

### 6.1 状态码定义
```go
const (
    ReadConfig = 1001          // 读取配置失败
    ReadRaw = 1002            // 读取原始数据失败
    ConnDatabase = 1003       // 连接数据库失败
    CreateTbale = 1004        // 创建表失败
    TableNotExsit = 1005      // 表不存在
    NotAlterColumn = 1006     // 修改列失败
    QueryLastDate = 1007      // 查询最后日期失败
    InitDatabase = 1008       // 初始化数据库失败
    ReConnDatabase = 1009     // 重连数据库失败
    TableSchmeFail = 1010     // 表结构获取失败
)
```

### 6.2 错误日志示例
```
配置不存在，表名: etc_renew_record Backinfo长度: 0
获取配置失败，程序退出，表名: etc_renew_record
查询表结构差异失败，源表: src_table 目标表: dest_table 错误: connection lost
执行表结构修改SQL失败: ALTER TABLE ... 错误: syntax error
```

## 7. 功能特性

### 7.1 自动表创建
- 当目标表不存在时，系统会自动创建表
- 支持从缓存中获取表结构定义
- 完整的创建过程日志记录

### 7.2 表结构同步
- 自动检测表结构差异
- 支持字段添加和修改
- 临时表机制确保操作安全

### 7.3 模式切换
- 支持运行时动态切换执行模式
- 通过API接口控制任务行为
- 全局配置与任务配置的智能合并

### 7.4 资源管理
- 数据库连接的正确关闭
- 临时表的自动清理
- 查询结果集的及时释放

## 8. 监控和调试

### 8.1 日志级别
- **DEBUG**: 详细的执行步骤和参数信息
- **ERROR**: 错误信息和异常状态

### 8.2 关键监控点
1. 任务调度频率和执行时间
2. 数据库连接状态
3. 表创建和结构同步状态
4. 数据迁移进度和性能
5. 错误发生频率和类型

### 8.3 日志文件位置
- 主日志文件: `/Users/<USER>/GolandProjects/mysql_archive/mysql_archive/mysql_archive.log`
- 控制台输出: 实时显示关键操作信息

## 9. API接口

### 9.1 任务控制接口
```bash
# 激活任务并设置模式
curl -H "Content-Type: application/json" -X POST \
  -d '{"running": true,"cmode": "sync","status": 0}' \
  "127.0.0.1:14445/api/information/v1/mtriecs"

# 启用debug日志
curl -H "Content-Type: application/json" -X POST \
  -d '{"running": true,"cmode": "sync","status": 0,"debug_log": true}' \
  "127.0.0.1:14445/api/information/v1/mtriecs"

# 禁用debug日志（默认状态）
curl -H "Content-Type: application/json" -X POST \
  -d '{"running": true,"cmode": "sync","status": 0,"debug_log": false}' \
  "127.0.0.1:14445/api/information/v1/mtriecs"

# 查询任务状态（包含版本信息）
curl "127.0.0.1:14445/api/information/v1/mtriecs"

# 查询版本信息
curl "127.0.0.1:14445/api/version"
```

### 9.2 支持的模式
- `sync`: 表结构同步模式
- `day`: 按天归档模式
- `row`: 按行归档模式

### 9.3 版本信息接口

#### 9.3.1 专用版本API
```bash
curl http://127.0.0.1:14445/api/version
```

**响应示例**:
```json
{
  "status": "ok",
  "timestamp": 1752556819,
  "version_info": {
    "major": 1,
    "minor": 2,
    "patch": 0,
    "build": "v1.2.0-dirty",
    "build_time": "2025-07-15 13:19:43",
    "git_commit": "1d409b2",
    "version_string": "v1.2.0-dirty"
  }
}
```

#### 9.3.2 命令行版本查询
```bash
# 完整版本信息
./mysql_archive_local -version

# 简写方式
./mysql_archive_local -v
```

**输出示例**:
```
MySQL Archive v1.2.0-dirty
构建版本: v1.2.0-dirty
构建时间: 2025-07-15 13:19:43
Git提交: 1d409b2
版本详情: v1.2.0
```

### 9.4 Debug日志控制
- `debug_log: true`: 启用详细的debug日志，显示完整的执行链路
- `debug_log: false`: 禁用debug日志，只输出错误日志（默认状态）
- 日志级别可以通过API动态切换，无需重启程序
- 配置会持久化保存，程序重启后会恢复上次的设置

#### 9.4.1 Debug日志功能特性
1. **动态控制**: 通过API实时开启/关闭debug日志
2. **持久化配置**: 设置会保存到本地存储，重启后自动恢复
3. **完整链路跟踪**: 启用后可以看到从调度器到数据库操作的每一步
4. **性能友好**: 禁用时不会产生debug日志的性能开销
5. **环境变量支持**: 也可以通过`DEBUG_LOG=true`环境变量启用

#### 9.4.2 使用建议
- **生产环境**: 建议默认关闭debug日志，只在需要排查问题时临时开启
- **开发环境**: 可以开启debug日志来了解系统运行细节
- **问题排查**: 遇到问题时先开启debug日志，复现问题后再分析日志
- **性能监控**: 长期运行时建议关闭debug日志以减少I/O开销

## 10. 总结

MySQL Archive系统通过完整的调用链路日志记录，实现了：

1. **完整的执行跟踪**: 从调度器到数据库操作的每一步都有详细记录
2. **智能错误处理**: 多层次的错误检测和状态管理
3. **灵活的模式切换**: 支持运行时动态调整执行策略
4. **自动化运维**: 表自动创建、结构同步等功能
5. **高可观测性**: 丰富的日志信息便于问题定位和性能优化
6. **动态日志控制**: 支持运行时开启/关闭debug日志，平衡调试需求和性能要求
7. **完善的版本管理**: 支持语义化版本控制，提供多种方式查询版本信息

### 10.1 日志管理最佳实践

#### 生产环境
- 默认关闭debug日志（`debug_log: false`）
- 只输出错误日志，减少I/O开销
- 遇到问题时临时开启debug日志进行排查
- 问题解决后及时关闭debug日志

#### 开发环境
- 可以开启debug日志（`debug_log: true`）
- 便于了解系统内部运行机制
- 有助于功能开发和问题调试

#### 运维监控
- 通过API接口动态控制日志级别
- 配置持久化，重启后自动恢复设置
- 结合日志文件和控制台输出进行全面监控
- 完善的版本信息追踪，便于问题定位和版本管理

#### 版本管理
- 语义化版本控制（MAJOR.MINOR.PATCH）
- 构建时版本信息注入
- 多种版本查询方式（命令行、API）
- Git提交信息和构建时间记录

通过这套完整的日志体系和动态控制机制，运维人员可以：
- 清晰地了解系统的运行状态
- 快速定位和解决问题
- 进行有效的性能调优
- 在调试需求和系统性能之间找到最佳平衡
