package utils

import (
	"fmt"
	"github.com/thedevsaddam/gojsonq"
	"mysql_archive/pkg/zlog"
	"net"
	"regexp"
	"strings"
	"sync"
	"time"
)

//func GetServiceConf(YamlFile string) map[string][]string {
//   // 将文件内容绑定对象
//   configMap := make(map[string][]string)
//   config, _ := until.ReadFile(YamlFile)
//   err := yaml.Unmarshal(config, configMap)
//   if err != nil {
//       log.Fatalf("Unmarshal: %v when to map", err)
//   }
//   return configMap
//}

// 切片去重
func removeDuplicateElement(languages []string) []string {
	result := make([]string, 0, len(languages))
	temp := map[string]struct{}{}
	for _, item := range languages {
		if _, ok := temp[item]; !ok {
			temp[item] = struct{}{}
			result = append(result, item)
		}
	}
	return result
}

// Cond 查询条件
func Cond(rows []map[string][]string) string {
	var ct []string
	for _, row := range rows {
		for cName, v := range row {
			ct = append(ct, fmt.Sprintf("%s in ('%s')", cName, strings.Join(v, "','")))
		}
	}
	return strings.Join(ct, " and ")
}

// Batch 分批
//func Batch(rows []map[string]string, BatchNum int) map[int][]map[string][]string {
//	values := map[int][]map[string][]string{}
//	batchNo := 0
//	for i := 0; i <= len(rows); i += BatchNum {
//		start := i
//		end := i + BatchNum
//		args := map[string][]string{}
//
//		if end >= len(rows) {
//			end = len(rows)
//		}
//
//		if end <= len(rows) {
//			for _, v := range rows[start:end] {
//				for k, v1 := range v {
//					args[k] = append(removeDuplicateElement(args[k]), v1)
//				}
//			}
//			values[batchNo] = append(values[batchNo], args)
//			batchNo = batchNo + 1
//		} else {
//			for _, v := range rows[start:end] {
//				for k, v1 := range v {
//					args[k] = append(removeDuplicateElement(args[k]), v1)
//				}
//			}
//			values[batchNo] = append(values[batchNo], args)
//			batchNo = batchNo + 1
//		}
//	}
//	return values
//}

func Batch(rows []map[string]string, BatchNum int) map[int][]map[string][]string {
	values := map[int][]map[string][]string{}
	batchNo := 0
	for i := 0; i <= len(rows); i += BatchNum {
		start := i
		end := i + BatchNum
		args := map[string][]string{}

		if end >= len(rows) {
			end = len(rows)
		}

		if end <= len(rows) {
			for _, v := range rows[start:end] {
				for k, v1 := range v {
					if v1 != "" {
						args[k] = append(removeDuplicateElement(args[k]), v1)
					}
				}
			}
			if len(args) > 0 {
				values[batchNo] = append(values[batchNo], args)
				batchNo = batchNo + 1
			}
		} else {
			for _, v := range rows[start:end] {
				for k, v1 := range v {
					if v1 != "" {
						args[k] = append(removeDuplicateElement(args[k]), v1)
					}
				}
			}
			if len(args) > 0 {
				values[batchNo] = append(values[batchNo], args)
				batchNo = batchNo + 1
			}
		}
	}
	return values
}

func JsonString2Map(json string, req string) interface{} {
	jq := gojsonq.New().JSONString(json)
	res := jq.Find(req)
	return res
}

func Keys_(m *sync.Map) []string {
	keys := []string{}
	m.Range(func(key, value interface{}) bool {
		keys = append(keys, key.(string))
		return true
	})
	return keys
}

func levenshteinDistance(s1, s2 string) int {
	m := len(s1)
	n := len(s2)

	// 初始化二维数组
	dp := make([][]int, m+1)
	for i := 0; i <= m; i++ {
		dp[i] = make([]int, n+1)
		dp[i][0] = i
	}
	for j := 0; j <= n; j++ {
		dp[0][j] = j
	}

	// 动态规划计算编辑距离
	for i := 1; i <= m; i++ {
		for j := 1; j <= n; j++ {
			cost := 0
			if s1[i-1] != s2[j-1] {
				cost = 1
			}
			dp[i][j] = min(dp[i-1][j]+1, dp[i][j-1]+1, dp[i-1][j-1]+cost)
		}
	}

	return dp[m][n]
}

func min(a, b, c int) int {
	if a <= b && a <= c {
		return a
	} else if b <= a && b <= c {
		return b
	}
	return c
}

func FindMostSimilarValue(target string, values []string) string {
	if len(values) == 0 {
		return ""
	}

	highestSimilarity := -1
	mostSimilarValue := ""

	for _, value := range values {
		similarity := levenshteinDistance(strings.ToLower(target), strings.ToLower(value))
		if highestSimilarity == -1 || similarity < highestSimilarity {
			highestSimilarity = similarity
			mostSimilarValue = value
		}
	}

	return mostSimilarValue
}

// IsAfterSevenDaysAgo 判断日期是否在7天之内（保留向后兼容）
func IsAfterSevenDaysAgo(dateStr string) bool {
	return IsAfterDaysAgo(dateStr, 7)
}

// IsAfterDaysAgo 判断日期是否在指定天数之内
func IsAfterDaysAgo(dateStr string, days int) bool {
	// 获取当前日期
	currentDate := time.Now()

	// 计算当前日期减去指定天数的日期
	dateDaysAgo := currentDate.AddDate(0, 0, -(days + 1))
	zlog.Debug("日期对比:", currentDate, dateDaysAgo, "间隔天数:", days)

	// 从表名中提取日期部分
	extractedDate := extractDateFromTableName(dateStr)
	if extractedDate == "" {
		zlog.Error("无法从表名中提取日期:", dateStr)
		return false
	}
	zlog.Debug("从表名中提取的日期:", extractedDate)

	// 尝试不同的日期格式
	dateFormats := []string{
		"20060102",   // YYYYMMDD
		"200601",     // YYYYMM
		"2006-01-02", // YYYY-MM-DD
		"2006-01",    // YYYY-MM
	}

	var dateToCompare time.Time
	var err error

	// 遍历所有的日期格式，尝试解析
	for _, layout := range dateFormats {
		dateToCompare, err = time.Parse(layout, extractedDate)
		if err == nil {
			zlog.Debug("成功解析日期，格式:", layout, "日期:", dateToCompare)
			break
		}
	}

	// 如果所有的格式都无法解析，则返回错误
	if err != nil {
		zlog.Error("日期转换出错:", err, "原始字符串:", dateStr, "提取的日期:", extractedDate)
		return false
	}

	// 判断提供的日期是否大于指定天数前的日期
	isAfter := dateToCompare.After(dateDaysAgo)
	zlog.Debug("日期比较结果:", dateToCompare, "是否在", days, "天内:", isAfter)
	return isAfter
}

// extractDateFromTableName 从表名中提取日期部分
func extractDateFromTableName(tableName string) string {
	// 使用正则表达式匹配表名中的日期部分
	// 支持以下格式：
	// - YYYYMMDD (8位数字)
	// - YYYYMM (6位数字)
	// - YYYY_MM_DD (带下划线)
	// - YYYY_MM (带下划线)

	// 匹配8位数字 (YYYYMMDD)
	re8 := regexp.MustCompile(`(\d{8})`)
	if matches := re8.FindStringSubmatch(tableName); len(matches) > 1 {
		return matches[1]
	}

	// 匹配6位数字 (YYYYMM)
	re6 := regexp.MustCompile(`(\d{6})`)
	if matches := re6.FindStringSubmatch(tableName); len(matches) > 1 {
		return matches[1]
	}

	// 匹配带下划线的日期格式 YYYY_MM_DD
	reUnderscore8 := regexp.MustCompile(`(\d{4}_\d{2}_\d{2})`)
	if matches := reUnderscore8.FindStringSubmatch(tableName); len(matches) > 1 {
		// 转换为YYYYMMDD格式
		return strings.ReplaceAll(matches[1], "_", "")
	}

	// 匹配带下划线的日期格式 YYYY_MM
	reUnderscore6 := regexp.MustCompile(`(\d{4}_\d{2})`)
	if matches := reUnderscore6.FindStringSubmatch(tableName); len(matches) > 1 {
		// 转换为YYYYMM格式
		return strings.ReplaceAll(matches[1], "_", "")
	}

	return ""
}

func GetLocalIP() string {
	addrList, err := net.InterfaceAddrs()
	if err != nil {
		return ""
	}

	for _, address := range addrList {
		if ipNet, ok := address.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
			if ipNet.IP.To4() != nil {
				return ipNet.IP.String()
			}
		}
	}
	return ""
}
