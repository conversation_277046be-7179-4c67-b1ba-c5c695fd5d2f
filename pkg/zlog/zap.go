package zlog

import (
	"fmt"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"os"
	"strings"
)

var (
	// Logger Logger为zap提供的原始日志，但使用起来比较烦，有强类型约束
	Logger *zap.Logger
	// Sugar SugaredLogger为zap提供的一个通用性更好的日志组件，作为本项目的核心日志组件
	Sugar *zap.SugaredLogger
	// 定时删除
)

// PathExists 判断文件夹是否存在
func PathExists(path string) {
	// check
	if _, err := os.Stat(path); err == nil {

	} else {
		err := os.MkdirAll(path, 0755)
		if err != nil {
			return
		}
	}
	// check again
	if _, err := os.Stat(path); err == nil {

	}
}

func Debug(msg ...any) {
	if Sugar == nil {
		// 如果Sugar未初始化，检查环境变量决定是否输出debug日志
		logLevel := strings.ToLower(os.Getenv("LOG_LEVEL"))
		if logLevel == "debug" || os.Getenv("DEBUG_LOG") == "true" {
			fmt.Println("[DEBUG]", fmt.Sprintln(msg...))
		}
		return
	}
	Sugar.Debug(fmt.Sprintln(msg...))
	err := Sugar.Sync()
	if err != nil {
		return
	}
}

func Info(msg ...any) {
	if Sugar == nil {
		// 如果Sugar未初始化，检查环境变量决定是否输出info日志
		logLevel := strings.ToLower(os.Getenv("LOG_LEVEL"))
		if logLevel == "debug" || logLevel == "info" {
			fmt.Println("[INFO]", fmt.Sprintln(msg...))
		}
		return
	}
	Sugar.Info(fmt.Sprintln(msg...))
	err := Sugar.Sync()
	if err != nil {
		return
	}
}

func Warn(msg ...any) {
	if Sugar == nil {
		// 如果Sugar未初始化，检查环境变量决定是否输出warn日志
		logLevel := strings.ToLower(os.Getenv("LOG_LEVEL"))
		if logLevel == "debug" || logLevel == "info" || logLevel == "warn" {
			fmt.Println("[WARN]", fmt.Sprintln(msg...))
		}
		return
	}
	Sugar.Warn(fmt.Sprintln(msg...))
	err := Sugar.Sync()
	if err != nil {
		return
	}
}

func Error(msg ...any) {
	if Sugar == nil {
		// 如果Sugar未初始化，使用标准错误输出
		fmt.Fprintln(os.Stderr, "[ERROR]", fmt.Sprintln(msg...))
		return
	}
	Sugar.Error(fmt.Sprintln(msg...))
	err := Sugar.Sync()
	if err != nil {
		return
	}
}
func Init(FileName, LogDir string) {
	PathExists(LogDir)
	// 日志暂时只开放一个配置 - 配置文件路径，有需要可以后续开放
	hook := lumberjack.Logger{
		Filename:   LogDir + "/" + FileName, // 日志文件路径
		MaxSize:    500,                     // 每个日志文件保存的最大尺寸 单位：M
		MaxBackups: 7,                       // 日志文件最多保存多少个备份
		MaxAge:     7,                       // 文件最多保存多少天
		Compress:   true,                    // 是否压缩
	}
	w := zapcore.AddSync(&hook)
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder

	// 根据环境变量设置日志级别
	logLevel := getLogLevelFromEnv()

	core := zapcore.NewCore(
		zapcore.NewConsoleEncoder(encoderConfig),
		w,
		logLevel,
	)

	Logger = zap.New(core, zap.AddCaller())
	Sugar = Logger.Sugar()

	// 输出日志级别信息
	levelName := getLogLevelName(logLevel)
	fmt.Printf("日志系统初始化完成，当前日志级别: %s\n", levelName)
	return
}

// getLogLevelFromEnv 从环境变量获取日志级别
func getLogLevelFromEnv() zapcore.Level {
	// 优先检查新的LOG_LEVEL环境变量
	if logLevelStr := os.Getenv("LOG_LEVEL"); logLevelStr != "" {
		switch strings.ToLower(logLevelStr) {
		case "debug":
			return zapcore.DebugLevel
		case "info":
			return zapcore.InfoLevel
		case "warn", "warning":
			return zapcore.WarnLevel
		case "error":
			return zapcore.ErrorLevel
		}
	}

	// 向后兼容：检查旧的DEBUG_LOG环境变量
	if os.Getenv("DEBUG_LOG") == "true" {
		return zapcore.DebugLevel
	}

	// 默认为Error级别
	return zapcore.ErrorLevel
}

// getLogLevelName 获取日志级别名称
func getLogLevelName(level zapcore.Level) string {
	switch level {
	case zapcore.DebugLevel:
		return "DEBUG"
	case zapcore.InfoLevel:
		return "INFO"
	case zapcore.WarnLevel:
		return "WARN"
	case zapcore.ErrorLevel:
		return "ERROR"
	default:
		return "UNKNOWN"
	}
}

// UpdateLogLevel 动态更新日志级别
func UpdateLogLevel(enableDebug bool) {
	PathExists("log")
	hook := lumberjack.Logger{
		Filename:   "log/mysql_archive.log",
		MaxSize:    500,
		MaxBackups: 7,
		MaxAge:     7,
		Compress:   true,
	}
	w := zapcore.AddSync(&hook)
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder

	logLevel := zapcore.ErrorLevel
	if enableDebug {
		logLevel = zapcore.DebugLevel
	}

	core := zapcore.NewCore(
		zapcore.NewConsoleEncoder(encoderConfig),
		w,
		logLevel,
	)

	Logger = zap.New(core, zap.AddCaller())
	Sugar = Logger.Sugar()
}

// UpdateLogLevelByString 通过字符串动态更新日志级别
func UpdateLogLevelByString(levelStr string) {
	PathExists("log")
	hook := lumberjack.Logger{
		Filename:   "log/mysql_archive.log",
		MaxSize:    500,
		MaxBackups: 7,
		MaxAge:     7,
		Compress:   true,
	}
	w := zapcore.AddSync(&hook)
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder

	var logLevel zapcore.Level
	switch strings.ToLower(levelStr) {
	case "debug":
		logLevel = zapcore.DebugLevel
	case "info":
		logLevel = zapcore.InfoLevel
	case "warn", "warning":
		logLevel = zapcore.WarnLevel
	case "error":
		logLevel = zapcore.ErrorLevel
	default:
		logLevel = zapcore.ErrorLevel
	}

	core := zapcore.NewCore(
		zapcore.NewConsoleEncoder(encoderConfig),
		w,
		logLevel,
	)

	Logger = zap.New(core, zap.AddCaller())
	Sugar = Logger.Sugar()

	levelName := getLogLevelName(logLevel)
	fmt.Printf("日志级别已更新为: %s\n", levelName)
}
