CREATE TABLE  if not exists `split_province_exit_jour_detail_202208` (
  `n_ex_date` int(8) DEFAULT NULL,
  `n_ex_lane_id` int(6) DEFAULT NULL,
  `n_ex_serial_no` int(6) DEFAULT NULL,
  `vc_pass_id` varchar(40) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '通行标识ID',
  `vc_trade_id` varchar(38) CHARACTER SET utf8mb4 NOT NULL,
  `n_province_id` int(6) NOT NULL DEFAULT '0',
  `n_toll_support` int(6) DEFAULT NULL,
  `n_marks_count` int(4) DEFAULT NULL,
  `vc_toll_intervals_group` text CHARACTER SET utf8mb4 COMMENT '收费单元编号组合',
  `vc_trans_time_group` text CHARACTER SET utf8mb4 COMMENT '收费单元时间组合',
  `vc_charge_fee_group` text CHARACTER SET utf8mb4 COMMENT '收费单元交易金额组合',
  `vc_discount_fee_group` text CHARACTER SET utf8mb4 COMMENT '收费单元优惠金额组合',
  `vc_rate_version_group` text CHARACTER SET utf8mb4 COMMENT '收费单元费率版本号组合',
  `d_toll_fee` decimal(8,2) DEFAULT NULL,
  `d_discount_fee` decimal(8,2) DEFAULT NULL,
  `c_mot_send_flag` char(1) CHARACTER SET utf8mb4 DEFAULT NULL,
  `c_center_send_flag` char(1) CHARACTER SET utf8mb4 DEFAULT NULL,
  `c_midcenter_send_flag` char(1) CHARACTER SET utf8mb4 DEFAULT NULL,
  `c_subcenter_send_flag` char(1) CHARACTER SET utf8mb4 DEFAULT NULL,
  `c_station_send_flag` char(1) CHARACTER SET utf8mb4 DEFAULT NULL,
  `n_row_no` tinyint(4) DEFAULT '1' COMMENT '省域顺序号',
  `vc_station_path` text CHARACTER SET utf8mb4 COMMENT '原始收费标识信息',
  `n_vehicle_sign` int(11) DEFAULT NULL COMMENT '车辆识别标识',
  `c_source` char(1) CHARACTER SET utf8mb4 DEFAULT '0' COMMENT '站省传输标志',
  UNIQUE KEY `uk_split` (`n_ex_date`,`n_ex_lane_id`,`n_ex_serial_no`,`n_row_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;