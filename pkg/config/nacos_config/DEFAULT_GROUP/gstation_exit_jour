CREATE TABLE if not exists `exit_jour_202208` (
  `n_en_date` int(8) DEFAULT NULL COMMENT '入口日期',
  `n_en_time` int(6) DEFAULT NULL COMMENT '入口时间',
  `c_en_shift_code` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '入口班次代码',
  `n_en_station_id` int(8) DEFAULT NULL COMMENT '入口收费站代码',
  `n_en_card_id` char(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '入口卡号',
  `c_en_vehicle_class` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '入口车型',
  `n_ex_date` int(8) NOT NULL COMMENT '出口日期',
  `n_ex_time` int(6) DEFAULT NULL COMMENT '出口时间',
  `n_ex_serial_no` int(6) NOT NULL COMMENT '出口流水号',
  `n_ex_lane_id` int(6) NOT NULL COMMENT '出口车道代码',
  `c_ex_shift_code` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '出口班次代码',
  `n_ex_op_code` int(8) DEFAULT NULL COMMENT '出口操作员代码',
  `c_ex_vehicle_class` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '出口车型',
  `c_ex_vehicle_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '出口车辆种类',
  `c_ex_card_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '出口卡状态',
  `c_other_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '其它卡类型',
  `n_other_id` char(22) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '其它卡号',
  `vc_marks` varchar(170) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '卡内标识',
  `d_toll` decimal(8,2) DEFAULT NULL COMMENT '通行费金额',
  `d_unpaid` decimal(8,2) DEFAULT NULL COMMENT '欠款金额',
  `d_forfeit` decimal(8,2) DEFAULT NULL COMMENT '罚款金额',
  `d_agency_toll` decimal(8,2) DEFAULT NULL COMMENT '代收金额',
  `vc_agencys` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '代收费代码',
  `c_category` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车辆类别',
  `c_pay_way` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '付款方式',
  `n_bill_no` bigint(20) DEFAULT NULL COMMENT '发票号码',
  `d_bef_balance` decimal(8,2) DEFAULT NULL COMMENT '储值卡余额',
  `n_prn_times` int(2) DEFAULT NULL COMMENT '重打次数',
  `n_box_id` int(8) DEFAULT NULL COMMENT '交易日期',
  `c_station_send_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收费站发送标志',
  `n_zone_time` int(2) DEFAULT NULL COMMENT '时间段',
  `n_card_lane_id` int(6) DEFAULT NULL COMMENT '卡内车道号',
  `n_card_serial_no` int(6) DEFAULT NULL COMMENT '卡内流水号',
  `n_card_op_code` int(8) DEFAULT NULL COMMENT '卡内收费员',
  `c_card_license` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '卡内车牌号码',
  `c_ex_license` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '出口车牌号码',
  `d_card_toll` decimal(8,2) DEFAULT NULL COMMENT '卡内通行费金额',
  `d_card_agency_toll` decimal(8,2) DEFAULT NULL COMMENT '卡内代收金额',
  `c_part_vehicle_type` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '过程车种',
  `c_special_type` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '特殊操作类型',
  `vc_card_agencys` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '卡内代收费代码',
  `d_card_forfeit` decimal(8,2) DEFAULT NULL COMMENT '卡内罚款金额',
  `n_temp` int(8) DEFAULT NULL COMMENT '预留',
  `c_temp` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '预留',
  `c_posserialno` char(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'pos流水号',
  `c_cardno` char(19) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '付费卡号',
  `c_bankexamno` char(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '银行检索参考号',
  `c_ticket_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '通行券类型',
  `d_weight` decimal(8,2) DEFAULT NULL COMMENT '载货总重',
  `d_over_weight` decimal(8,2) DEFAULT NULL COMMENT '超载重量',
  `c_verifycode` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '校验串',
  `n_expsamid` int(8) DEFAULT NULL COMMENT '出口psam卡号',
  `n_enpsamid` int(8) DEFAULT NULL COMMENT '入口psam卡号',
  `c_lanetype` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车道类型',
  `c_cacu_way` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '计费方式',
  `vc_tac` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'TAC码',
  `vc_issue_code` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发行方',
  `d_feelength` decimal(8,4) DEFAULT NULL COMMENT '计费里程',
  `d_fare0` decimal(8,2) DEFAULT NULL COMMENT '里程费',
  `d_fare1` decimal(8,2) DEFAULT NULL COMMENT '叠加费',
  `d_fare2` decimal(8,2) DEFAULT NULL COMMENT '车次费',
  `vc_fix_marks` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修正标识',
  `vc_fix_marks_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '标识修正状态',
  `n_ex_cpu_psamid` int(8) DEFAULT NULL COMMENT '出口psam卡号',
  `n_vehicle_seats` int(6) DEFAULT NULL COMMENT '座位数',
  `n_trade_time` int(8) DEFAULT NULL COMMENT '交易时间',
  `n_trade_speed` decimal(8,2) DEFAULT NULL COMMENT '车速',
  `n_batch_no` varchar(16) DEFAULT NULL COMMENT '发票批次号',
  `n_temp1` int(8) DEFAULT NULL COMMENT '预留',
  `c_temp1` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '预留',
  `n_temp2` int(8) DEFAULT NULL COMMENT '预留',
  `c_temp2` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '预留',
  `c_make_ticket` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否开票',
  `n_en_sys_date` int(8) DEFAULT NULL COMMENT '入口系统日期',
  `n_favouredpolicy_type` int(8) DEFAULT NULL COMMENT '优惠政策类型',
  `n_mb_trade_date` int(8) DEFAULT NULL COMMENT '支付日期',
  `n_mb_trade_time` int(9) DEFAULT NULL COMMENT '支付时间',
  `vc_temp3` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '预留',
  `vc_iden_license` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '识别车牌号码',
  `n_marks_mode` int(6) DEFAULT NULL COMMENT '路标计费模式',
  `c_en_lane_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '入口车道类型',
  `c_en_category` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '入口客货',
  `vc_axle_specialtype` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '计重特情',
  `n_exaxlecount` int(6) DEFAULT NULL COMMENT '出口轴数',
  `vc_cardver` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '卡片版本',
  `n_algorithmidentifier` int(6) DEFAULT NULL COMMENT '算法标识',
  `vc_stationhex` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收费站Hex串',
  `vc_ex_lane_hex` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '出口Hex',
  `vc_en_lane_hex` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '入口车道HEX',
  `d_enweight` decimal(8,2) DEFAULT NULL COMMENT '入口重量',
  `n_enaxlecount` int(6) DEFAULT NULL COMMENT '入口轴数',
  `vc_enaxletype` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '入口轴型',
  `d_enpermittedweight` decimal(8,2) DEFAULT NULL COMMENT '入口限重',
  `n_enspecialtruck` int(6) DEFAULT NULL COMMENT '特殊货车信息',
  `vc_markhex` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '路径Hex串',
  `d_originaltotalfee` decimal(8,2) DEFAULT NULL COMMENT '原始总金额',
  `d_originalpassfare` decimal(8,2) DEFAULT NULL COMMENT '原始通行费',
  `d_originalagencyfare` decimal(8,2) DEFAULT NULL COMMENT '原始代收费',
  `d_originalfare0` decimal(8,2) DEFAULT NULL COMMENT '原始里程费',
  `d_originalfare1` decimal(8,2) DEFAULT NULL COMMENT '原始叠加费',
  `d_originalfare2` decimal(8,2) DEFAULT NULL COMMENT '预留',
  `vc_rateversion` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '计费模块版本号',
  `vc_obumac` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'OBU/CPC MAC地址',
  `vc_obu_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'OBU ',
  `n_electricalpercent` int(6) DEFAULT NULL COMMENT 'CPC卡电量百分比',
  `n_multiprovince` int(6) DEFAULT NULL COMMENT '是否多省交易',
  `n_toll_support` int(6) DEFAULT NULL COMMENT '计费支撑',
  `n_obu_sign` int(6) DEFAULT NULL COMMENT 'OBU类型',
  `vc_pass_id` varchar(40) DEFAULT NULL COMMENT '通行标识ID',
  `n_en_vehicle_usertype` int(10) DEFAULT NULL COMMENT '入口车辆用户类型',
  `n_ex_vehicle_usertype` int(10) DEFAULT NULL COMMENT '出口车辆用户类型',
  `n_direction` int(11) DEFAULT NULL COMMENT '行驶方向',
  `vc_vehicle_sign_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '关联图片流水号',
  `d_trans_fee` decimal(8,2) DEFAULT NULL COMMENT '卡面代收金额',
  `n_sign_status` int(11) DEFAULT NULL COMMENT '标记状态',
  `vc_trade_id` varchar(38) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '出口交易代码',
  `ts_timestamp` datetime DEFAULT NULL COMMENT '出口处理时间',
  `vc_exhex` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'vc_exhex',
  `vc_enhex` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'vc_enhex',
  `vc_obusn` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'vc_obusn',
  `d_svc_balance` decimal(8,2) DEFAULT NULL COMMENT 'd_svc_balance',
  `c_mot_send_flag` tinyint(4) DEFAULT '0' COMMENT '部中心发送标志',
  `c_center_send_flag` tinyint(4) DEFAULT '0' COMMENT '省中心发送标志',
  `n_vehicle_sign` int(10) DEFAULT NULL,
  `n_vehicle_class_gb` int(10) DEFAULT NULL,
  `vc_cert_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `n_gantry_lines` tinyint(3) unsigned DEFAULT NULL,
  `n_detail_lines` tinyint(3) unsigned DEFAULT NULL,
  `d_zj_fee` decimal(8,2) DEFAULT NULL COMMENT '浙江段收费金额',
  `n_obu_count_local` smallint(6) DEFAULT NULL COMMENT 'OBU省内交易成功数',
  `n_obu_count_total` smallint(6) DEFAULT NULL COMMENT 'OBU全程交易成功数',
  `n_obu_count_nocard` smallint(6) DEFAULT NULL COMMENT 'OBU标签无卡次数',
  `n_obu_count_provs` tinyint(3) unsigned DEFAULT NULL,
  `vc_obu_en_station` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'OBU省内入口站码',
  `n_obu_distance` bigint(20) DEFAULT NULL COMMENT 'OBU计费里程',
  `d_prov_pay_fee` decimal(8,2) DEFAULT NULL COMMENT '省内应收金额',
  `d_prov_fee` decimal(8,2) DEFAULT NULL COMMENT '省内实收金额',
  `d_cpu_fee_total` decimal(8,2) DEFAULT NULL COMMENT 'CPU全程累计金额',
  `vc_obu_ef04_rec` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'EF04通行记录315-485',
  `vc_prov_ids` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'OBU通行省份明细',
  `vc_prov_fees` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'OBU分省金额明细',
  `vc_short_fare_ver` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '最短路径版本',
  `d_short_fee_total` decimal(8,2) DEFAULT NULL COMMENT '最短路径金额',
  `vc_special_type_gb` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '特情类型（部标）',
  `n_agency_gantry_num` tinyint(3) unsigned DEFAULT NULL,
  `d_agency_gantry_fee` decimal(8,2) DEFAULT NULL COMMENT '代收门架交易金额',
  `n_display_amount_type` tinyint(4) DEFAULT NULL COMMENT '费显显示金额类型',
  `n_trans_pay_type` tinyint(3) unsigned DEFAULT NULL,
  `d_short_fee_mileage` decimal(8,4) DEFAULT NULL COMMENT '全网最短计费里程',
  `d_fee_rate` decimal(8,2) DEFAULT NULL COMMENT '交易金额占比',
  `n_exit_fee_type` tinyint(3) unsigned DEFAULT NULL,
  `d_total_pay_fee` decimal(8,2) DEFAULT NULL COMMENT '全程累计应收金额',
  `d_total_fee` decimal(8,2) DEFAULT NULL COMMENT '全程累计实收金额',
  `n_trade_mode` tinyint(3) unsigned DEFAULT NULL,
  `vc_appoint` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '预约ID',
  `vc_prov_od` varchar(1500) DEFAULT NULL COMMENT 'CPC分省OD明细',
  `d_prov_pass_fee` decimal(8,2) DEFAULT NULL COMMENT '省内通行费实收',
  `ts_center_send_time` datetime DEFAULT NULL COMMENT '发送省中心kafka时间',
  `ts_mot_send_time` datetime DEFAULT NULL COMMENT '发送部站接口时间',
  `c_road_send_flag` tinyint(4) DEFAULT '1' COMMENT '路中心发送标志',
  `ts_road_send_time` datetime DEFAULT NULL COMMENT '路中心发送时间',
  `db_timestamp` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  PRIMARY KEY (`vc_trade_id`),
  UNIQUE KEY `UK_exit_jour` (`n_ex_serial_no`,`n_ex_lane_id`,`n_ex_date`),
  KEY `IDX_exit_jour_n_other_id` (`n_other_id`),
  KEY `IDX_exit_jour_c_ex_license` (`c_ex_license`),
  KEY `IDX_exit_jour_n_en_card_id` (`n_en_card_id`),
  KEY `IDX_exit_jour_vc_obu_id` (`vc_obu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;