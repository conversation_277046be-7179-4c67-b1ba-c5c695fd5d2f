CREATE TABLE  if not exists `image_jour_202207` (
  `vc_pic_id` varchar(38) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '牌识流水号',
  `vc_lane_id` varchar(21) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '收费车道编号',
  `n_lane_id` int(6) NOT NULL COMMENT '省内车道编号',
  `ts_timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '抓拍时间',
  `n_date` int(8) NOT NULL COMMENT '拍摄日期',
  `n_time` int(6) NOT NULL COMMENT '拍摄时间',
  `vc_hour_batch_no` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '小时批次号',
  `c_shoot_position` tinyint(4) NOT NULL COMMENT '拍摄位置',
  `n_lane_num` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '物理车道编码',
  `n_station_id` int(4) DEFAULT NULL COMMENT '收费站编码',
  `vc_license` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '车牌号码',
  `n_image_size` int(8) NOT NULL COMMENT '车辆全景图片大小',
  `vc_image_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车辆全景图片相对路径',
  `n_license_image_size` int(8) DEFAULT NULL COMMENT '车牌图片大小',
  `vc_license_image_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车牌图片相对路径',
  `n_bin_image_size` int(8) DEFAULT NULL COMMENT '二值图片大小',
  `vc_bin_image_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '二值化图相对路径',
  `vc_verify_code` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '00000000' COMMENT '校验码',
  `vc_trade_id` varchar(38) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '计费交易编号',
  `vc_pass_id` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '通行标识ID',
  `ts_station_db_time` datetime NOT NULL DEFAULT '2019-01-01 01:00:00' COMMENT '门架后台入库时间',
  `c_mot_send_flag` tinyint(4) DEFAULT '0' COMMENT '部中心发送标志',
  `c_center_send_flag` tinyint(4) DEFAULT '0' COMMENT '省中心发送标志',
  `c_midcenter_send_flag` tinyint(4) DEFAULT '0' COMMENT '路段中心发送标志',
  `c_subcenter_send_flag` tinyint(4) DEFAULT '0' COMMENT '分中心发送标志',
  `c_station_send_flag` tinyint(4) DEFAULT '0' COMMENT '收费站发送标志',
  `n_check_serial_no` decimal(6,0) DEFAULT NULL COMMENT '检测流水号，备用',
  `c_check` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '校验串，备用',
  `c_shift_code` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '工班号，1-早班；2-中班；3-晚班',
  `n_op_code` decimal(8,0) DEFAULT NULL COMMENT '操作员号',
  `n_vehicle_speed` int(11) DEFAULT NULL COMMENT '车速，单位：千米/小时',
  `n_direction` int(11) DEFAULT NULL COMMENT '行驶方向，1位数字 1-上行 2-下行',
  `n_serial_no` decimal(6,0) DEFAULT NULL COMMENT '车道流水号',
  `n_sys_date` int(8) DEFAULT NULL COMMENT '系统时间',
  `vc_gantry_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '承载门架编号',
  `vc_gantry_hex` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '承载门架Hex值',
  `n_iden_type` int(11) DEFAULT NULL COMMENT '识别车型',
  `vc_iden_model` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '识别车辆型号',
  PRIMARY KEY (`vc_pic_id`),
  KEY `idx_vc_lane_id` (`vc_lane_id`),
  KEY `idx_vc_license` (`vc_license`),
  KEY `idx_n_date` (`n_date`),
  KEY `idx_n_time` (`n_time`),
  KEY `idx_ts_timestamp` (`ts_timestamp`),
  KEY `idx_n_station_id` (`n_station_id`),
  KEY `idx_vc_trade_id` (`vc_trade_id`),
  KEY `idx_vc_pass_id` (`vc_pass_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;