gantry_jour:
  interval: 7
  datecloum: ts_trans_time
  cloumtype: datetime
  PRIMARY: vc_trade_id
  batch: 100
  conditions: c_center_send_flag=2

gantry_image_jour:
  interval: 7
  datecloum: ts_timestamp
  cloumtype: datetime
  PRIMARY: vc_pic_id
  batch: 100
  conditions: c_center_send_flag=2

entry_jour:
  interval: 7
  datecloum: n_en_date
  cloumtype: int
  PRIMARY: vc_trade_id
  batch: 100
  conditions: c_center_send_flag=2

exit_jour:
  interval: 7
  datecloum: n_ex_date
  cloumtype: int
  PRIMARY: vc_trade_id
  batch: 100
  conditions: c_center_send_flag=2

image_jour:
  interval: 7
  datecloum: n_date
  cloumtype: int
  PRIMARY: vc_pic_id
  batch: 100
  conditions: c_center_send_flag=2

split_province_exit_jour_detail:
  interval: 7
  datecloum: n_ex_date
  cloumtype: int
  PRIMARY: n_ex_date,n_ex_lane_id,n_ex_serial_no,n_row_no
  batch: 100
  conditions: c_center_send_flag=2

etc_info:
  interval: 7
  datecloum: n_date
  cloumtype: int
  PRIMARY: n_date,n_lane_id,n_serial_no,n_time,n_time_ms
  batch: 100
  conditions: c_sf1=2

lane_status_info:
  interval: 7
  datecloum: n_date
  cloumtype: int
  PRIMARY: n_lane_id,n_date,n_record_no,n_time
  batch: 100
  conditions: c_center_send_flag=2

weight_jour:
  interval: 7
  datecloum: n_date
  cloumtype: int
  PRIMARY: id
  batch: 100
  conditions: n_upload_status=2