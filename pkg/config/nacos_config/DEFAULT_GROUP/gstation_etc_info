CREATE TABLE if not exists `etc_info_202208` (
  `n_date` int(8) NOT NULL,
  `n_lane_id` int(6) NOT NULL,
  `n_serial_no` int(6) NOT NULL,
  `n_time` int(6) NOT NULL,
  `n_time_ms` int(6) NOT NULL,
  `n_deduct_date` int(8) NOT NULL,
  `n_deduct_time` int(6) NOT NULL,
  `c_license` char(12) DEFAULT NULL,
  `c_license_color` char(1) DEFAULT '0',
  `c_category` char(1) DEFAULT '0',
  `c_vehicle_class` char(1) DEFAULT '0',
  `n_vehicle_user_type` int(8) DEFAULT NULL,
  `vc_obu_status` varchar(4) DEFAULT NULL,
  `vc_obu_provider1` varchar(4) DEFAULT NULL,
  `vc_obu_provider2` varchar(4) DEFAULT NULL,
  `c_obu_contract_type` char(1) DEFAULT NULL,
  `vc_obu_contract_ver` varchar(2) DEFAULT NULL,
  `vc_obu_contract_no` varchar(16) DEFAULT NULL,
  `vc_obu_mac_id` varchar(8) DEFAULT NULL,
  `n_obu_issue_date` int(8) DEFAULT NULL,
  `n_obu_expire_date` int(8) DEFAULT NULL,
  `vc_obu_marks` varchar(50) DEFAULT NULL,
  `vc_cpu_status` varchar(4) DEFAULT NULL,
  `vc_cpu_provider1` varchar(4) DEFAULT NULL,
  `vc_cpu_provider2` varchar(4) DEFAULT NULL,
  `n_cpu_type` int(8) DEFAULT NULL,
  `vc_cpu_ver` varchar(2) DEFAULT NULL,
  `vc_cpu_net_id` varchar(4) DEFAULT NULL,
  `vc_cpu_card_id` varchar(22) DEFAULT NULL,
  `n_cpu_issue_date` int(8) DEFAULT NULL,
  `n_cpu_expire_date` int(8) DEFAULT NULL,
  `vc_cpu_marks` varchar(50) DEFAULT NULL,
  `d_balance_before` decimal(12,2) DEFAULT NULL,
  `d_deduction` decimal(12,2) DEFAULT NULL,
  `d_balance_after` decimal(12,2) DEFAULT NULL,
  `c_lane_type` char(1) DEFAULT '0',
  `c_tran_category` char(1) DEFAULT '0',
  `vc_terminal_no` varchar(12) DEFAULT NULL,
  `vc_terminal_tranno` varchar(8) DEFAULT NULL,
  `vc_cpu_tranno` varchar(4) DEFAULT NULL,
  `vc_tac` varchar(8) DEFAULT NULL,
  `n_tran_status` int(8) DEFAULT NULL,
  `vc_tran_event` varchar(4) DEFAULT NULL,
  `n_rsu_manu_id` int(6) DEFAULT NULL,
  `vc_rsu_id` varchar(6) DEFAULT NULL,
  `n_start_date` int(8) DEFAULT NULL,
  `n_start_time` int(6) DEFAULT NULL,
  `n_begin_time` int(6) DEFAULT NULL,
  `n_begin_time_ms` int(6) DEFAULT NULL,
  `n_duration` int(8) DEFAULT NULL,
  `n_mm` int(8) DEFAULT NULL,
  `d_ttl` decimal(9,3) DEFAULT NULL,
  `d_tor` decimal(9,3) DEFAULT NULL,
  `d_tcr` decimal(9,3) DEFAULT NULL,
  `d_tow` decimal(9,3) DEFAULT NULL,
  `d_tcw` decimal(9,3) DEFAULT NULL,
  `d_tv` decimal(9,3) DEFAULT NULL,
  `d_tce` decimal(9,3) DEFAULT NULL,
  `d_td` decimal(9,3) DEFAULT NULL,
  `d_ti` decimal(9,3) DEFAULT NULL,
  `d_stor` decimal(9,3) DEFAULT NULL,
  `d_stcr` decimal(9,3) DEFAULT NULL,
  `d_stow` decimal(9,3) DEFAULT NULL,
  `d_stcw` decimal(9,3) DEFAULT NULL,
  `c_sow` char(1) DEFAULT NULL,
  `c_scw` char(1) DEFAULT NULL,
  `c_sor` char(1) DEFAULT NULL,
  `c_scr` char(1) DEFAULT NULL,
  `n_tm1` int(8) DEFAULT NULL,
  `n_tm2` int(8) DEFAULT NULL,
  `d_tm3` decimal(9,3) DEFAULT NULL,
  `d_tm4` decimal(9,3) DEFAULT NULL,
  `vc_tm5` varchar(10) DEFAULT NULL,
  `vc_tm6` varchar(10) DEFAULT NULL,
  `c_sf1` char(1) DEFAULT NULL,
  `c_sf2` char(1) DEFAULT NULL,
  `c_sf3` char(1) DEFAULT NULL,
  `c_sf4` char(1) DEFAULT NULL,
  UNIQUE KEY `UK_etc_info` (`n_date`,`n_lane_id`,`n_serial_no`,`n_time`,`n_time_ms`),
  KEY `IDX_etc_info_c_sf1` (`c_sf1`),
  KEY `IDX_etc_info_c_sf4` (`c_sf4`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;