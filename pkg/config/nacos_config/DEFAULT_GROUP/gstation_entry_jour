CREATE TABLE if not exists `entry_jour_202208` (
  `n_en_date` int(8) NOT NULL COMMENT '入口日期',
  `n_en_time` int(6) DEFAULT NULL COMMENT '入口时间',
  `n_en_serial_no` int(6) NOT NULL COMMENT '入口车道流水号',
  `c_en_shift_code` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '入口班次代码',
  `n_en_op_code` int(8) DEFAULT NULL COMMENT '入口操作员代码',
  `n_en_lane_id` int(6) NOT NULL COMMENT '入口车道代码',
  `n_en_card_id` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '入口卡号',
  `c_en_vehicle_class` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '入口车型',
  `c_en_vehicle_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '入口车辆种类',
  `n_box_id` int(8) DEFAULT NULL COMMENT '卡箱号码',
  `c_station_send_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收费站发送标志',
  `n_zone_time` int(2) DEFAULT NULL COMMENT '时间段',
  `c_license` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '手动输入车牌号码',
  `c_part_vehicle_type` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '过程车种',
  `c_special_type` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '特殊操作类型',
  `n_temp` int(8) DEFAULT NULL COMMENT '预留',
  `c_temp` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '预留',
  `c_ticket_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '通行券类型',
  `n_psamid` int(8) DEFAULT NULL COMMENT 'PSAM卡号',
  `c_verifycode` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '校验串',
  `c_lanetype` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '车道类型',
  `c_category` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客货车类型',
  `n_cpu_psamid` int(8) DEFAULT NULL COMMENT 'psam卡号',
  `vc_issue_code` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发行方',
  `n_vehicle_seats` int(6) DEFAULT NULL COMMENT '座位数',
  `c_terminal_number` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '终端编号',
  `c_terminal_tranno` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '终端交易序号',
  `n_tranno` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'CPU卡交易序号',
  `n_trans_date` int(8) DEFAULT NULL COMMENT '交易日期',
  `n_trans_time` int(6) DEFAULT NULL COMMENT '交易时间',
  `vc_tac` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'TAC码',
  `n_card_type` int(8) DEFAULT NULL COMMENT '卡片类型',
  `c_en_license` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '识别车牌号码',
  `c_license_color` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '卡内车牌颜色',
  `n_vehicle_usertype` int(10) DEFAULT NULL COMMENT '车辆用户类型',
  `n_trade_time` int(8) DEFAULT NULL COMMENT '交易时间',
  `n_trade_speed` decimal(8,2) DEFAULT NULL COMMENT '车速',
  `n_temp1` int(8) DEFAULT NULL COMMENT '预留',
  `c_temp1` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '预留',
  `n_temp2` int(8) DEFAULT NULL COMMENT '预留',
  `c_temp2` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '预留',
  `c_card_license` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '卡内车牌号码',
  `vc_cardver` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '卡片版本',
  `n_algorithmidentifier` int(6) DEFAULT NULL COMMENT '算法标识',
  `vc_stationhex` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收费站Hex串',
  `vc_en_lane_hex` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '入口HEX串',
  `d_enweight` decimal(8,2) DEFAULT NULL COMMENT '入口重量',
  `n_enaxlecount` int(6) DEFAULT NULL COMMENT '入口轴数',
  `vc_enaxletype` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '入口轴型',
  `d_enpermittedweight` decimal(8,2) DEFAULT NULL COMMENT '入口限重',
  `n_enspecialtruck` int(6) DEFAULT NULL COMMENT '特殊货车信息',
  `vc_obumac` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'OBU/CPC MAC地址',
  `vc_obusn` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'OBU ',
  `n_electricalpercent` int(6) DEFAULT NULL COMMENT 'CPC卡电量百分比',
  `n_obu_sign` int(6) DEFAULT NULL COMMENT 'OBU单/双片标识',
  `vc_pass_id` varchar(40) DEFAULT NULL COMMENT '通行标识ID',
  `n_direction` int(11) DEFAULT NULL COMMENT '行驶方向',
  `vc_vehicle_sign_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '关联图片流水号',
  `vc_license_fix` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修正车牌',
  `d_trans_fee` decimal(8,2) DEFAULT NULL COMMENT '卡面代收金额',
  `n_sign_status` int(11) DEFAULT NULL COMMENT '标记状态',
  `vc_trade_id` varchar(38) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '计费交易编号',
  `ts_timestamp` datetime DEFAULT NULL COMMENT '入口处理时间',
  `vc_enhex` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'vc_enhex',
  `vc_obu_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'vc_obu_id',
  `c_mot_send_flag` tinyint(4) DEFAULT '0' COMMENT '部中心发送标志',
  `c_center_send_flag` tinyint(4) DEFAULT '0' COMMENT '省中心发送标志',
  `n_vehicle_sign` int(10) DEFAULT NULL,
  `n_vehicle_class_gb` int(10) DEFAULT NULL,
  `vc_cert_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `vc_special_type_gb` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '特殊操作类型',
  `n_gantry_lines` tinyint(3) unsigned DEFAULT NULL,
  `n_trade_mode` tinyint(3) unsigned DEFAULT NULL,
  `ts_center_send_time` datetime DEFAULT NULL COMMENT '发送省中心kafka时间',
  `ts_mot_send_time` datetime DEFAULT NULL COMMENT '发送部站接口时间',
  `c_road_send_flag` tinyint(4) DEFAULT '1' COMMENT '路中心发送标志',
  `ts_road_send_time` datetime DEFAULT NULL COMMENT '路中心发送时间',
  PRIMARY KEY (`vc_trade_id`),
  UNIQUE KEY `UK_entry_jour` (`n_en_serial_no`,`n_en_lane_id`,`n_en_date`),
  KEY `IDX_entry_jour_c_en_license` (`c_en_license`),
  KEY `IDX_entry_jour_n_en_card_id` (`n_en_card_id`),
  KEY `IDX_entry_jour_vc_obu_id` (`vc_obu_id`),
  KEY `IDX_entry_jour_c_license` (`c_license`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;