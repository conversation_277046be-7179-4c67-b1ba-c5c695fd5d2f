package leveldb

import (
	"github.com/nacos-group/nacos-sdk-go/common/logger"
	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/util"
	"mysql_archive/pkg/zlog"
	"path/filepath"
	"strings"
	"time"
)

var (
	err error
)

type LevelDBStore struct {
	Db *leveldb.DB
}

func (store *LevelDBStore) Init() {
	absPath, _ := filepath.Abs("data.ldb")

	// 尝试多次打开数据库，处理资源占用的情况
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		store.Db, err = leveldb.OpenFile(absPath, nil)
		if err == nil {
			logger.Debug("LevelDB初始化成功，路径:", absPath)
			return
		}

		// 如果是资源占用错误，等待一段时间后重试
		if strings.Contains(err.Error(), "resource temporarily unavailable") ||
			strings.Contains(err.<PERSON>rror(), "lock") {
			if i < maxRetries-1 {
				logger.Debug("数据库被占用，等待重试...", "尝试次数:", i+1)
				time.Sleep(time.Duration(i+1) * time.Second)
				continue
			}
		}

		// 其他错误或重试次数用完
		logger.Error("LevelDB初始化失败:", err.Error(), "尝试次数:", i+1)
		if i == maxRetries-1 {
			logger.Error("LevelDB初始化最终失败，将使用内存模式")
			// 可以考虑使用内存存储作为fallback
			store.Db = nil
		}
	}
}
func (store *LevelDBStore) KvPut(key string, value any) (err error) {
	if store.Db == nil {
		zlog.Error("LevelDB未初始化，无法执行Put操作")
		return nil // 静默失败，避免程序崩溃
	}

	err = store.Db.Put([]byte(key), value.([]byte), nil)
	if err != nil {
		zlog.Error("LevelDB Put操作失败:", err.Error())
		return err
	}
	return nil
}

func (store *LevelDBStore) KvGet(key string) (v string, err error) {
	if store.Db == nil {
		zlog.Error("LevelDB未初始化，无法执行Get操作")
		return "", nil // 返回空值，避免程序崩溃
	}

	x, err := store.Db.Get([]byte(key), nil)
	if err != nil {
		zlog.Error("kv Get: %v", err)
	}
	return string(x), err
}

func (store *LevelDBStore) KvDelete(key string) (err error) {
	if store.Db == nil {
		zlog.Error("LevelDB未初始化，无法执行Delete操作")
		return nil // 静默失败，避免程序崩溃
	}

	err = store.Db.Delete([]byte(key), nil)
	if err != nil {
		zlog.Error("kv delete: %v", err)
	}
	return nil
}

func (store *LevelDBStore) KvScanKey(key string) map[string]string {
	xx := map[string]string{}
	if store.Db == nil {
		zlog.Error("LevelDB未初始化，无法执行ScanKey操作")
		return xx // 返回空map，避免程序崩溃
	}

	iter := store.Db.NewIterator(util.BytesPrefix([]byte(key)), nil)
	for iter.Next() {
		zlog.Debug(string(iter.Key()), ":", string(iter.Value()))
		xx[string(iter.Key())] = string(iter.Value())
	}
	return xx
}

// 判断key是否存在
func (store *LevelDBStore) KvExist(key string) bool {
	if store.Db == nil {
		zlog.Error("LevelDB未初始化，无法执行Exist操作")
		return false // 返回false，避免程序崩溃
	}

	if ok, _ := store.Db.Has([]byte(key), nil); !ok {
		return false
	}
	return true
}

// Close 关闭数据库连接
func (store *LevelDBStore) Close() error {
	if store.Db != nil {
		err := store.Db.Close()
		if err != nil {
			zlog.Error("关闭LevelDB失败:", err.Error())
			return err
		}
		store.Db = nil
		zlog.Debug("LevelDB已关闭")
	}
	return nil
}
