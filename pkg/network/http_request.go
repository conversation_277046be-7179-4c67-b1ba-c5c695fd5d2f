package network

import (
	"crypto/tls"
	"io"
	"mysql_archive/pkg/zlog"
	"net/http"
	"net/http/cookiejar"
	"strings"
)

type Request struct {
}

func (r *Request) Get(url string) (StatusCode int, body []byte) {
	resp, err := http.Get(url)
	if err != nil {
		return 0, nil
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			zlog.Error("连接关闭失败")
		}
	}(resp.Body)
	body, _ = io.ReadAll(resp.Body)
	return resp.StatusCode, body
}

func (r *Request) Post(url string, requesBody string) (StatusCode int, body []byte) {
	resp, err := http.Post(url,
		"application/x-www-form-urlencoded",
		strings.NewReader(requesBody))
	if err != nil {
		zlog.Error("POST请求失败", err.Error())
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {

		}
	}(resp.Body)
	body, err = io.ReadAll(resp.Body)
	if err != nil {
		// handle error
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			zlog.Error("连接关闭失败")
		}
	}(resp.Body)
	return resp.StatusCode, body
}

func (r *Request) PostHttps(url string, requesBody string) (StatusCode int, body []byte) {
	cookieJar, _ := cookiejar.New(nil)
	tr := &http.Transport{
		DisableKeepAlives: true,
		TLSClientConfig:   &tls.Config{InsecureSkipVerify: true},
	}
	c := &http.Client{
		Jar:       cookieJar,
		Transport: tr,
	}
	resp, err := c.Post(url,
		"application/x-www-form-urlencoded",
		strings.NewReader(requesBody))
	if err != nil {
		zlog.Error("POST请求失败", err.Error())
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)
	body, err = io.ReadAll(resp.Body)
	if err != nil {
		// handle error
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			zlog.Error("连接关闭失败")
		}
	}(resp.Body)
	return resp.StatusCode, body
}

func (r *Request) GetHttps(url string) (StatusCode int, body []byte) {
	zlog.Debug("【网络请求】==================== 开始HTTPS请求 ====================")
	zlog.Debug("【网络请求】步骤1: 准备HTTPS GET请求")
	zlog.Debug("【网络请求】目标URL:", url)

	zlog.Debug("【网络请求】步骤2: 创建HTTP客户端")
	cookieJar, _ := cookiejar.New(nil)
	tr := &http.Transport{
		DisableKeepAlives: true,
		TLSClientConfig:   &tls.Config{InsecureSkipVerify: true},
	}
	c := &http.Client{
		Jar:       cookieJar,
		Transport: tr,
	}
	zlog.Debug("【网络请求】步骤2: HTTP客户端创建完成 ")
	zlog.Debug("【网络请求】配置: DisableKeepAlives=true, InsecureSkipVerify=true")

	zlog.Debug("【网络请求】步骤3: 发送GET请求")
	resp, err := c.Get(url)
	if err != nil {
		zlog.Error("【网络请求】步骤3: GET请求失败 ")
		zlog.Error("【网络请求】失败URL:", url)
		zlog.Error("【网络请求】失败原因:", err.Error())

		// Check if resp is not nil before trying to close resp.Body
		if resp != nil {
			zlog.Debug("【网络请求】步骤4: 清理响应资源")
			err := resp.Body.Close()
			if err != nil {
				zlog.Error("【网络请求】响应体关闭失败:", err.Error())
			} else {
				zlog.Debug("【网络请求】响应体关闭成功")
			}
		}
		zlog.Error("【网络请求】==================== HTTPS请求失败 ====================")
		return 0, nil
	}

	zlog.Debug("【网络请求】步骤3: GET请求成功 ")
	zlog.Debug("【网络请求】响应状态码:", resp.StatusCode)
	zlog.Debug("【网络请求】响应头Content-Type:", resp.Header.Get("Content-Type"))
	zlog.Debug("【网络请求】响应头Content-Length:", resp.Header.Get("Content-Length"))

	zlog.Debug("【网络请求】步骤4: 读取响应体")
	var err2 error
	body, err2 = io.ReadAll(resp.Body)
	if err2 != nil {
		zlog.Error("【网络请求】步骤4: 读取响应体失败 ")
		zlog.Error("【网络请求】读取错误:", err2.Error())
		zlog.Error("【网络请求】==================== HTTPS请求失败 ====================")
		return resp.StatusCode, nil
	}

	zlog.Debug("【网络请求】步骤4: 响应体读取完成 ")
	zlog.Debug("【网络请求】响应体长度:", len(body), "字节")

	// 显示响应内容预览
	if len(body) > 0 {
		if len(body) > 200 {
			zlog.Debug("【网络请求】响应内容预览:", string(body))
		} else {
			zlog.Debug("【网络请求】完整响应内容:", string(body))
		}
	} else {
		zlog.Debug("【网络请求】响应体为空")
	}
	zlog.Debug("【网络请求】步骤5: 清理资源")
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			zlog.Error("【网络请求】响应体关闭失败:", err.Error())
		} else {
			zlog.Debug("【网络请求】响应体关闭成功")
		}
	}(resp.Body)

	c.CloseIdleConnections()
	zlog.Debug("【网络请求】空闲连接已关闭")

	zlog.Debug("【网络请求】步骤6: HTTPS请求完成")
	zlog.Debug("【网络请求】最终状态码:", resp.StatusCode)
	zlog.Debug("【网络请求】最终响应长度:", len(body), "字节")
	zlog.Info("【网络请求】==================== HTTPS请求成功 ====================")
	return resp.StatusCode, body
}
