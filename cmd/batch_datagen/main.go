package main

import (
	"database/sql"
	"fmt"
	"log"
	"math/rand"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

const (
	// 数据库配置
	DB_HOST     = "127.0.0.1"
	DB_PORT     = "3306"
	DB_USER     = "hsyq"
	DB_PASSWORD = "Hsyq@123"
	DB_NAME     = "gstation"

	// 数据生成配置
	TOTAL_RECORDS = 1000000 // 每个表 100 万条记录
	DATE_DAYS     = 30      // 日期覆盖 30 天
	BATCH_SIZE    = 1000    // 批量插入大小
)

func main() {
	log.Println("批量数据生成工具启动")

	// 设置随机种子
	rand.Seed(time.Now().UnixNano())

	// 连接数据库
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&parseTime=true&loc=Local",
		DB_USER, DB_PASSWORD, DB_HOST, DB_PORT, DB_NAME)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	// 设置连接池参数
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(5)

	// 测试连接
	if err := db.Ping(); err != nil {
		log.Fatalf("数据库连接测试失败: %v", err)
	}

	log.Println("数据库连接成功")

	// 要处理的表
	tables := []string{"a", "b", "c"}

	log.Printf("开始为表 %v 生成数据", tables)
	log.Printf("配置: 每表 %d 条记录，日期范围 %d 天，批量大小 %d", TOTAL_RECORDS, DATE_DAYS, BATCH_SIZE)

	startTime := time.Now()

	// 为每个表生成数据
	for _, table := range tables {
		tableStart := time.Now()

		if err := generateDataForTable(db, table); err != nil {
			log.Fatalf("生成表 %s 数据失败: %v", table, err)
		}

		tableElapsed := time.Since(tableStart)
		log.Printf("表 %s 完成，耗时: %v", table, tableElapsed)
	}

	totalElapsed := time.Since(startTime)
	totalRecords := len(tables) * TOTAL_RECORDS

	log.Printf("所有数据生成完成！")
	log.Printf("总记录数: %d 条", totalRecords)
	log.Printf("总耗时: %v", totalElapsed)
	log.Printf("平均速度: %.0f 条/秒", float64(totalRecords)/totalElapsed.Seconds())
}

// 生成随机字符串
func randomString(length int) string {
	const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ**********"
	result := make([]byte, length)
	for i := range result {
		result[i] = chars[rand.Intn(len(chars))]
	}
	return string(result)
}

// 生成随机日期（过去30天内）
func randomDate() time.Time {
	now := time.Now()
	daysAgo := rand.Intn(DATE_DAYS)
	hoursAgo := rand.Intn(24)
	minutesAgo := rand.Intn(60)

	return now.AddDate(0, 0, -daysAgo).
		Add(-time.Duration(hoursAgo) * time.Hour).
		Add(-time.Duration(minutesAgo) * time.Minute)
}

// 生成车牌号
func generateLicensePlate() string {
	provinces := []string{"京", "津", "沪", "渝", "冀", "豫", "云", "辽", "黑", "湘"}
	letters := "ABCDEFGHJKLMNPQRSTUVWXYZ"
	numbers := "**********"

	province := provinces[rand.Intn(len(provinces))]
	letter := string(letters[rand.Intn(len(letters))])
	var plateNum string
	for k := 0; k < 5; k++ {
		if rand.Intn(2) == 0 {
			plateNum += string(letters[rand.Intn(len(letters))])
		} else {
			plateNum += string(numbers[rand.Intn(len(numbers))])
		}
	}
	return province + letter + plateNum
}

// 为单个表生成数据
func generateDataForTable(db *sql.DB, tableName string) error {
	log.Printf("开始为表 %s 生成数据...", tableName)

	// 计算批次数
	totalBatches := (TOTAL_RECORDS + BATCH_SIZE - 1) / BATCH_SIZE

	for batch := 0; batch < totalBatches; batch++ {
		batchStart := batch * BATCH_SIZE
		batchEnd := batchStart + BATCH_SIZE
		if batchEnd > TOTAL_RECORDS {
			batchEnd = TOTAL_RECORDS
		}

		currentBatchSize := batchEnd - batchStart

		// 生成批量插入SQL
		if err := insertBatch(db, tableName, batchStart, currentBatchSize); err != nil {
			return fmt.Errorf("批次 %d 插入失败: %v", batch, err)
		}

		// 进度报告
		completed := batchEnd
		if completed%50000 == 0 || completed == TOTAL_RECORDS {
			progress := float64(completed) / float64(TOTAL_RECORDS) * 100
			log.Printf("表 %s 进度: %d/%d (%.1f%%)", tableName, completed, TOTAL_RECORDS, progress)
		}
	}

	log.Printf("表 %s 数据生成完成，共 %d 条记录", tableName, TOTAL_RECORDS)
	return nil
}

// 批量插入数据
func insertBatch(db *sql.DB, tableName string, startIndex, batchSize int) error {
	var sql string
	var values []interface{}

	switch tableName {
	case "a":
		sql, values = buildTableABatch(startIndex, batchSize)
	case "b":
		sql, values = buildTableBBatch(startIndex, batchSize)
	case "c":
		sql, values = buildTableCBatch(startIndex, batchSize)
	default:
		return fmt.Errorf("不支持的表名: %s", tableName)
	}

	// 执行批量插入
	_, err := db.Exec(sql, values...)
	if err != nil {
		return fmt.Errorf("执行批量插入失败: %v", err)
	}

	return nil
}

// 构建表 a 的批量插入SQL
func buildTableABatch(startIndex, batchSize int) (string, []interface{}) {
	var valueStrings []string
	var values []interface{}

	for i := 0; i < batchSize; i++ {
		index := startIndex + i
		date := randomDate()
		nExDate := date.Year()*10000 + int(date.Month())*100 + date.Day()
		nExSerialNo := index + 10000000
		nExLaneId := (index % 1000) + 1
		vcLicense := generateLicensePlate()
		nAmount := rand.Float64() * 1000
		cStatus := fmt.Sprintf("%d", rand.Intn(10))
		dtCreateTime := randomDate()
		cSendFlag := fmt.Sprintf("%d", rand.Intn(2))

		valueStrings = append(valueStrings, "(?, ?, ?, ?, ?, ?, ?, ?)")
		values = append(values, nExDate, nExSerialNo, nExLaneId, vcLicense, nAmount, cStatus, dtCreateTime, cSendFlag)
	}

	sql := fmt.Sprintf("INSERT INTO a (n_ex_date, n_ex_serial_no, n_ex_lane_id, vc_license, n_amount, c_status, dt_create_time, c_send_flag) VALUES %s",
		strings.Join(valueStrings, ","))

	return sql, values
}

// 构建表 b 的批量插入SQL
func buildTableBBatch(startIndex, batchSize int) (string, []interface{}) {
	var valueStrings []string
	var values []interface{}

	for i := 0; i < batchSize; i++ {
		index := startIndex + i
		vcTradeId := fmt.Sprintf("TXN%d%08d", time.Now().Unix(), index)
		nLaneId := rand.Intn(10) + 1
		date := randomDate()
		nDate := date.Year()*10000 + int(date.Month())*100 + date.Day()
		nTime := date.Hour()*10000 + date.Minute()*100 + date.Second()
		vcLicense := generateLicensePlate()
		nAmount := rand.Float64() * 1000
		cVehicleType := fmt.Sprintf("%d", rand.Intn(5)+1)
		cSendFlag1 := fmt.Sprintf("%d", rand.Intn(2))
		cSendFlag2 := fmt.Sprintf("%d", rand.Intn(2))
		dtCreateTime := randomDate()

		valueStrings = append(valueStrings, "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")
		values = append(values, vcTradeId, nLaneId, nDate, nTime, vcLicense, nAmount, cVehicleType, cSendFlag1, cSendFlag2, dtCreateTime)
	}

	sql := fmt.Sprintf("INSERT INTO b (vc_trade_id, n_lane_id, n_date, n_time, vc_license, n_amount, c_vehicle_type, c_send_flag1, c_send_flag2, dt_create_time) VALUES %s",
		strings.Join(valueStrings, ","))

	return sql, values
}

// 构建表 c 的批量插入SQL
func buildTableCBatch(startIndex, batchSize int) (string, []interface{}) {
	var valueStrings []string
	var values []interface{}

	for i := 0; i < batchSize; i++ {
		index := startIndex + i
		vcId := fmt.Sprintf("ID%d%08d", time.Now().UnixNano(), index)
		nLaneId := rand.Intn(10) + 1
		date := randomDate()
		nDate := date.Year()*10000 + int(date.Month())*100 + date.Day()
		nTime := date.Hour()*10000 + date.Minute()*100 + date.Second()
		vcLicense := generateLicensePlate()
		cOperationType := fmt.Sprintf("%d", rand.Intn(5)+1)
		cStatus := fmt.Sprintf("%d", rand.Intn(10))

		var nAmount interface{}
		if rand.Intn(10) == 0 {
			nAmount = nil
		} else {
			nAmount = rand.Float64() * 1000
		}

		var vcRemark interface{}
		if rand.Intn(3) == 0 {
			vcRemark = nil
		} else {
			vcRemark = randomString(rand.Intn(50) + 10)
		}

		dtCreateTime := randomDate()
		cSendFlag1 := fmt.Sprintf("%d", rand.Intn(2))
		cSendFlag2 := fmt.Sprintf("%d", rand.Intn(2))

		valueStrings = append(valueStrings, "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")
		values = append(values, vcId, nLaneId, nDate, nTime, vcLicense, cOperationType, cStatus, nAmount, vcRemark, dtCreateTime, cSendFlag1, cSendFlag2)
	}

	sql := fmt.Sprintf("INSERT INTO c (vc_id, n_lane_id, n_date, n_time, vc_license, c_operation_type, c_status, n_amount, vc_remark, dt_create_time, c_send_flag1, c_send_flag2) VALUES %s",
		strings.Join(valueStrings, ","))

	return sql, values
}
