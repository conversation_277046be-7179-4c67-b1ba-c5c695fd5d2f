#/usr/lib/systemd/system/mysql_archive-.service
[Unit]
Description=mysql_archive
After=syslog.target
After=network.target
[Service]
Type=simple
PIDFile=/run/mysql_archive.pid
Restart=on-failure

KillMode=control-group
WorkingDirectory=/usr/local/mysql_archive
ExecStart=/usr/local/mysql_archive/bin/mysql_archive
ExecStop=/bin/kill -SIGTERM $MAINPID
RestartSec=10s
User=root
Group=root
LimitNOFILE=65535
LimitNPROC=65535
[Install]
WantedBy=multi-user.target
